<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive documentation for Linux kernel file systems subsystem (fs/)">
    <meta name="keywords" content="Linux kernel, file systems, VFS, ext4, Btrfs, XFS, page cache, directory operations">
    
    <title>File Systems (fs/) - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐧</text></svg>">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="../../index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="../../index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle expanded" aria-expanded="true">▼</button>
                    <a href="../index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu expanded">
                        <li class="nav-item">
                            <a href="../mm/index.html" class="nav-link">💾 Memory Management (mm/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../kernel/index.html" class="nav-link">⚙️ Core Kernel (kernel/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="index.html" class="nav-link active">📁 File Systems (fs/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../net/index.html" class="nav-link">🌐 Networking (net/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../drivers/index.html" class="nav-link">🔌 Device Drivers (drivers/)</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="../../patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
                
                <li class="nav-item">
                    <a href="../../api/index.html" class="nav-link">📚 API Reference</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="../../index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="../index.html">Core Subsystems</a>
                <span class="breadcrumb-separator">›</span>
                <span>File Systems (fs/)</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>📁 File Systems Subsystem (fs/)</h1>
                </div>
                <div class="section-content">
                    <p>
                        The file systems subsystem provides a unified interface for accessing files 
                        and directories across different storage devices and file system types. It 
                        implements the Virtual File System (VFS) layer that abstracts the differences 
                        between various file system implementations.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🎯 Core Responsibilities</h3>
                        </div>
                        <ul>
                            <li><strong>Virtual File System (VFS):</strong> Common interface for all file systems</li>
                            <li><strong>File Operations:</strong> Open, read, write, close, and seek operations</li>
                            <li><strong>Directory Management:</strong> Directory traversal, creation, and deletion</li>
                            <li><strong>Page Cache:</strong> Caching file data in memory for performance</li>
                            <li><strong>File System Implementations:</strong> Support for ext4, Btrfs, XFS, and others</li>
                            <li><strong>Mount/Unmount:</strong> File system mounting and namespace management</li>
                            <li><strong>File Locking:</strong> Advisory and mandatory file locking mechanisms</li>
                            <li><strong>Extended Attributes:</strong> Metadata storage beyond standard attributes</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Architecture Overview -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🏗️ File System Architecture</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="architecture-diagram">
                        <pre class="architecture-ascii">
┌─────────────────────────────────────────────────────────────┐
│                    User Space Applications                   │
├─────────────────────────────────────────────────────────────┤
│                     System Call Interface                   │
│                  (open, read, write, close)                 │
├─────────────────────────────────────────────────────────────┤
│                  Virtual File System (VFS)                  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │    File     │  Directory  │    Inode    │   Dentry    │  │
│  │ Operations  │ Operations  │   Cache     │   Cache     │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │    Page     │   Buffer    │    Mount    │    Lock     │  │
│  │    Cache    │    Cache    │ Management  │ Management  │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                File System Implementations                  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │    ext4     │    Btrfs    │     XFS     │    tmpfs    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │    proc     │    sysfs    │   debugfs   │     NFS     │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                      Block Layer                            │
│                   (Storage Devices)                         │
└─────────────────────────────────────────────────────────────┘
                        </pre>
                    </div>
                    
                    <h3>🔧 Core Components</h3>
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Virtual File System (VFS)</h4>
                                <p class="card-subtitle">Common interface layer</p>
                            </div>
                            <p>
                                Provides a unified interface that abstracts the differences between 
                                various file system implementations, allowing applications to work 
                                with any supported file system.
                            </p>
                            <p><strong>Key Files:</strong> <code>super.c</code>, <code>inode.c</code>, <code>dcache.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Page Cache</h4>
                                <p class="card-subtitle">File data caching</p>
                            </div>
                            <p>
                                Caches file data in memory to improve performance by reducing disk I/O. 
                                Implements read-ahead and write-back strategies for optimal throughput.
                            </p>
                            <p><strong>Key Files:</strong> <code>filemap.c</code>, <code>readahead.c</code>, <code>page-writeback.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Directory Entry Cache</h4>
                                <p class="card-subtitle">Path lookup optimization</p>
                            </div>
                            <p>
                                Caches directory entries (dentries) to speed up path lookups and 
                                reduce the overhead of traversing directory hierarchies.
                            </p>
                            <p><strong>Key Files:</strong> <code>dcache.c</code>, <code>namei.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">File System Implementations</h4>
                                <p class="card-subtitle">Specific file system types</p>
                            </div>
                            <p>
                                Individual file system implementations like ext4, Btrfs, XFS, and 
                                others that provide the actual storage and retrieval mechanisms.
                            </p>
                            <p><strong>Key Directories:</strong> <code>ext4/</code>, <code>btrfs/</code>, <code>xfs/</code></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- VFS Layer -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔗 Virtual File System (VFS) Layer</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>struct inode</h3>
                        <div class="api-signature">
                            <code>struct inode</code> - Represents a file system object
                        </div>
                        <p>
                            The inode structure represents a file system object (file, directory, 
                            device, etc.) and contains metadata about the object including 
                            permissions, timestamps, and size.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct inode {
    umode_t i_mode;               /* File type and permissions */
    unsigned short i_opflags;     /* Operation flags */
    kuid_t i_uid;                 /* User ID */
    kgid_t i_gid;                 /* Group ID */
    unsigned int i_flags;         /* File system flags */
    
    const struct inode_operations *i_op;  /* Inode operations */
    struct super_block *i_sb;     /* Super block */
    struct address_space *i_mapping; /* Page cache mapping */
    
    /* File attributes */
    unsigned long i_ino;          /* Inode number */
    union {
        const unsigned int i_nlink; /* Number of hard links */
        unsigned int __i_nlink;
    };
    dev_t i_rdev;                 /* Device number for special files */
    loff_t i_size;                /* File size in bytes */
    struct timespec64 i_atime;    /* Access time */
    struct timespec64 i_mtime;    /* Modification time */
    struct timespec64 i_ctime;    /* Change time */
    
    /* File operations */
    const struct file_operations *i_fop; /* File operations */
    
    /* File system specific data */
    void *i_private;              /* FS private data */
    
    /* Locking */
    spinlock_t i_lock;            /* Inode lock */
    struct rw_semaphore i_rwsem;  /* Read-write semaphore */
    
    /* Reference counting */
    atomic_t i_count;             /* Reference count */
    atomic_t i_dio_count;         /* Direct I/O count */
    
    /* Block allocation */
    blkcnt_t i_blocks;            /* Number of blocks */
    unsigned short i_bytes;       /* Bytes in last block */
    
    /* Security context */
    void *i_security;             /* Security module data */
    
    /* File system specific inode */
    union {
        struct pipe_inode_info *i_pipe;
        struct block_device *i_bdev;
        struct cdev *i_cdev;
        char *i_link;
        unsigned i_dir_seq;
    };
};</code></pre>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>struct file</h3>
                        <div class="api-signature">
                            <code>struct file</code> - Represents an open file
                        </div>
                        <p>
                            The file structure represents an open file descriptor and contains 
                            information about the current file position, access mode, and 
                            file operations.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct file {
    union {
        struct llist_node f_llist;
        struct rcu_head f_rcuhead;
        unsigned int f_iocb_flags;
    };
    
    struct path f_path;           /* Path to file */
    struct inode *f_inode;        /* Cached inode */
    const struct file_operations *f_op; /* File operations */
    
    /* File state */
    spinlock_t f_lock;            /* File lock */
    enum rw_hint f_write_hint;    /* Write hint */
    atomic_long_t f_count;        /* Reference count */
    unsigned int f_flags;         /* File flags (O_RDONLY, etc.) */
    fmode_t f_mode;               /* File mode */
    struct mutex f_pos_lock;      /* Position lock */
    loff_t f_pos;                 /* Current file position */
    struct fown_struct f_owner;   /* File owner */
    const struct cred *f_cred;    /* File credentials */
    struct file_ra_state f_ra;    /* Read-ahead state */
    
    u64 f_version;                /* Version number */
    void *f_security;             /* Security module data */
    
    /* Private data */
    void *private_data;           /* File system private data */
    
    /* Memory mapping */
    struct address_space *f_mapping; /* Page cache mapping */
    
    /* Error state */
    errseq_t f_wb_err;            /* Write-back error */
    errseq_t f_sb_err;            /* Super block error */
};</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- File Operations -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📄 File Operations</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>File Operation APIs</h3>
                        
                        <div class="api-function">
                            <div class="api-signature">
                                <code>ssize_t vfs_read(struct file *file, char __user *buf, size_t count, loff_t *pos)</code>
                            </div>
                            <p><strong>Purpose:</strong> Read data from a file through the VFS layer</p>
                            <p><strong>Parameters:</strong></p>
                            <ul>
                                <li><code class="parameter-type">struct file *</code> <strong>file</strong> - File to read from</li>
                                <li><code class="parameter-type">char __user *</code> <strong>buf</strong> - User buffer to read into</li>
                                <li><code class="parameter-type">size_t</code> <strong>count</strong> - Number of bytes to read</li>
                                <li><code class="parameter-type">loff_t *</code> <strong>pos</strong> - File position pointer</li>
                            </ul>
                            <p><strong>Returns:</strong> <code class="return-type">ssize_t</code> - Number of bytes read or error code</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* Example: Reading from a file in kernel space */
static ssize_t read_file_data(struct file *file, char *buffer, size_t size)
{
    ssize_t ret;
    loff_t pos = 0;
    mm_segment_t old_fs;
    
    if (!file || !buffer)
        return -EINVAL;
    
    /* Set kernel data segment */
    old_fs = get_fs();
    set_fs(KERNEL_DS);
    
    /* Read from file */
    ret = vfs_read(file, buffer, size, &pos);
    
    /* Restore user data segment */
    set_fs(old_fs);
    
    return ret;
}</code></pre>
                            </div>
                        </div>

                        <div class="api-function">
                            <div class="api-signature">
                                <code>struct file *filp_open(const char *filename, int flags, umode_t mode)</code>
                            </div>
                            <p><strong>Purpose:</strong> Open a file from kernel space</p>
                            <p><strong>Parameters:</strong></p>
                            <ul>
                                <li><code class="parameter-type">const char *</code> <strong>filename</strong> - Path to file</li>
                                <li><code class="parameter-type">int</code> <strong>flags</strong> - Open flags (O_RDONLY, O_WRONLY, etc.)</li>
                                <li><code class="parameter-type">umode_t</code> <strong>mode</strong> - File permissions for creation</li>
                            </ul>
                            <p><strong>Returns:</strong> <code class="return-type">struct file *</code> - File pointer or error</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* Example: Opening and reading a configuration file */
static int read_config_file(const char *path, char *buffer, size_t size)
{
    struct file *file;
    ssize_t bytes_read;
    int ret = 0;
    
    /* Open file for reading */
    file = filp_open(path, O_RDONLY, 0);
    if (IS_ERR(file)) {
        pr_err("Failed to open file %s: %ld\n", path, PTR_ERR(file));
        return PTR_ERR(file);
    }
    
    /* Read file contents */
    bytes_read = read_file_data(file, buffer, size - 1);
    if (bytes_read < 0) {
        pr_err("Failed to read file %s: %zd\n", path, bytes_read);
        ret = bytes_read;
    } else {
        buffer[bytes_read] = '\0';  /* Null terminate */
        pr_info("Read %zd bytes from %s\n", bytes_read, path);
    }
    
    /* Close file */
    filp_close(file, NULL);
    
    return ret;
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Page Cache -->
            <section class="content-section">
                <div class="section-header">
                    <h2>💾 Page Cache</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <p>
                        The page cache is a crucial component that caches file data in memory
                        to improve I/O performance. It implements read-ahead, write-back,
                        and memory reclaim strategies.
                    </p>

                    <div class="api-section">
                        <h3>Page Cache APIs</h3>

                        <div class="api-function">
                            <div class="api-signature">
                                <code>struct page *find_get_page(struct address_space *mapping, pgoff_t offset)</code>
                            </div>
                            <p><strong>Purpose:</strong> Find and get a page from the page cache</p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* Example: Reading a page from cache */
static int read_page_from_cache(struct inode *inode, pgoff_t index, char *buffer)
{
    struct page *page;
    void *kaddr;

    /* Try to find page in cache */
    page = find_get_page(inode->i_mapping, index);
    if (!page) {
        /* Page not in cache, need to read from storage */
        return read_page_from_storage(inode, index, buffer);
    }

    /* Page found in cache */
    kaddr = kmap_atomic(page);
    memcpy(buffer, kaddr, PAGE_SIZE);
    kunmap_atomic(kaddr);

    /* Release page reference */
    put_page(page);

    return 0;
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>Read-ahead Mechanism</h3>
                        <p>
                            The kernel implements intelligent read-ahead to prefetch pages
                            that are likely to be accessed soon, reducing I/O latency.
                        </p>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* Read-ahead state structure */
struct file_ra_state {
    pgoff_t start;                /* Where the last readahead started */
    unsigned int size;            /* Number of pages in last readahead */
    unsigned int async_size;      /* Async readahead size */
    unsigned int ra_pages;        /* Maximum readahead window */
    unsigned int mmap_miss;       /* Cache miss stat for mmap accesses */
    loff_t prev_pos;              /* Previous file position */
};

/* Trigger read-ahead */
static void trigger_readahead(struct file *file, pgoff_t offset, unsigned long req_size)
{
    struct file_ra_state *ra = &file->f_ra;

    /* Calculate read-ahead window */
    page_cache_sync_readahead(file->f_mapping, ra, file, offset, req_size);
}</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- File System Types -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🗂️ File System Types</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">ext4</h3>
                                <p class="card-subtitle">Fourth Extended File System</p>
                            </div>
                            <p>
                                The most widely used Linux file system, providing journaling,
                                large file support, and backward compatibility with ext2/ext3.
                            </p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Journaling for crash recovery</li>
                                <li>Large file and file system support (up to 1 EB)</li>
                                <li>Extent-based allocation</li>
                                <li>Delayed allocation</li>
                                <li>Online defragmentation</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Btrfs</h3>
                                <p class="card-subtitle">B-tree File System</p>
                            </div>
                            <p>
                                Modern copy-on-write file system with advanced features like
                                snapshots, compression, and built-in RAID support.
                            </p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Copy-on-write semantics</li>
                                <li>Snapshots and subvolumes</li>
                                <li>Built-in compression (zlib, lzo, zstd)</li>
                                <li>Checksumming for data integrity</li>
                                <li>Online resize and defragmentation</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">XFS</h3>
                                <p class="card-subtitle">High-performance journaling file system</p>
                            </div>
                            <p>
                                Designed for high-performance and scalability, particularly
                                suited for large files and high-throughput workloads.
                            </p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Allocation groups for scalability</li>
                                <li>Delayed allocation</li>
                                <li>Online defragmentation</li>
                                <li>Extended attributes</li>
                                <li>Real-time subvolume support</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">tmpfs</h3>
                                <p class="card-subtitle">Temporary file system in RAM</p>
                            </div>
                            <p>
                                Memory-based file system that stores files in virtual memory,
                                providing very fast access but no persistence across reboots.
                            </p>
                            <p><strong>Features:</strong></p>
                            <ul>
                                <li>Stored entirely in memory</li>
                                <li>Dynamic size adjustment</li>
                                <li>POSIX semantics</li>
                                <li>Used for /tmp, /dev/shm</li>
                                <li>Swap backing when memory is low</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Mount Management -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔗 Mount Management</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>Mount Operations</h3>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* File system registration */
static struct file_system_type my_fs_type = {
    .name = "myfs",
    .mount = my_fs_mount,
    .kill_sb = kill_block_super,
    .fs_flags = FS_REQUIRES_DEV,
};

/* Mount function implementation */
static struct dentry *my_fs_mount(struct file_system_type *fs_type,
                                  int flags, const char *dev_name,
                                  void *data)
{
    return mount_bdev(fs_type, flags, dev_name, data, my_fs_fill_super);
}

/* Super block operations */
static const struct super_operations my_fs_super_ops = {
    .alloc_inode = my_fs_alloc_inode,
    .destroy_inode = my_fs_destroy_inode,
    .write_inode = my_fs_write_inode,
    .put_super = my_fs_put_super,
    .statfs = my_fs_statfs,
    .remount_fs = my_fs_remount,
};

/* Register file system */
static int __init my_fs_init(void)
{
    return register_filesystem(&my_fs_type);
}

/* Unregister file system */
static void __exit my_fs_exit(void)
{
    unregister_filesystem(&my_fs_type);
}</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Configuration Options -->
            <section class="content-section">
                <div class="section-header">
                    <h2>⚙️ Configuration Options</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="config-grid">
                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_EXT4_FS</code></h4>
                            <p>Enable ext4 file system support</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_BTRFS_FS</code></h4>
                            <p>Enable Btrfs file system support</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_XFS_FS</code></h4>
                            <p>Enable XFS file system support</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_TMPFS</code></h4>
                            <p>Enable tmpfs (temporary file system)</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_PROC_FS</code></h4>
                            <p>Enable /proc file system</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_SYSFS</code></h4>
                            <p>Enable sysfs file system</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- File Organization -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📁 File Organization</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="file-tree">
                        <h3>fs/ Directory Structure</h3>
                        <pre class="file-listing">
fs/
├── <strong>super.c</strong>              # Super block operations
├── <strong>inode.c</strong>              # Inode management
├── <strong>dcache.c</strong>             # Directory entry cache
├── <strong>namei.c</strong>              # Path name lookup
├── <strong>file.c</strong>               # File operations
├── <strong>open.c</strong>               # File opening
├── <strong>read_write.c</strong>         # Read/write operations
├── <strong>file_table.c</strong>         # File descriptor table
├── <strong>filesystems.c</strong>        # File system registration
├── <strong>namespace.c</strong>          # Mount namespace
├── <strong>mount.h</strong>              # Mount definitions
├── <strong>internal.h</strong>           # Internal definitions
├── <strong>buffer.c</strong>             # Buffer cache
├── <strong>bio.c</strong>                # Block I/O
├── <strong>direct-io.c</strong>          # Direct I/O
├── <strong>mpage.c</strong>              # Multi-page I/O
├── <strong>splice.c</strong>             # Splice operations
├── <strong>sync.c</strong>               # Synchronization
├── <strong>utimes.c</strong>             # Time operations
├── <strong>xattr.c</strong>              # Extended attributes
├── <strong>posix_acl.c</strong>          # POSIX ACLs
├── <strong>locks.c</strong>              # File locking
├── <strong>fcntl.c</strong>              # File control
├── <strong>ioctl.c</strong>              # I/O control
├── <strong>readdir.c</strong>            # Directory reading
├── <strong>select.c</strong>             # File descriptor selection
├── <strong>poll.c</strong>               # Polling operations
├── <strong>signalfd.c</strong>           # Signal file descriptors
├── <strong>timerfd.c</strong>            # Timer file descriptors
├── <strong>eventfd.c</strong>            # Event file descriptors
├── <strong>userfaultfd.c</strong>        # User fault file descriptors
├── <strong>aio.c</strong>                # Asynchronous I/O
├── <strong>eventpoll.c</strong>          # Event polling (epoll)
├── <strong>anon_inodes.c</strong>        # Anonymous inodes
├── <strong>signalfd.c</strong>           # Signal file descriptors
├── <strong>pipe.c</strong>               # Pipe operations
├── <strong>fifo.c</strong>               # FIFO operations
├── <strong>stack.c</strong>              # File system stack
├── <strong>filesystems.c</strong>        # File system types
├── <strong>fs_struct.c</strong>          # File system structure
├── <strong>statfs.c</strong>             # File system statistics
├── <strong>fs_pin.c</strong>             # File system pinning
├── <strong>nsfs.c</strong>               # Namespace file system
├── <strong>libfs.c</strong>              # Library functions
├── <strong>mbcache.c</strong>            # Metadata block cache
├── <strong>posix_acl.c</strong>          # POSIX access control lists
├── <strong>coredump.c</strong>           # Core dump handling
├── <strong>binfmt_elf.c</strong>         # ELF binary format
├── <strong>binfmt_script.c</strong>      # Script binary format
├── <strong>exec.c</strong>               # Program execution
├── <strong>ext4/</strong>                # ext4 file system
│   ├── balloc.c            # Block allocation
│   ├── bitmap.c            # Bitmap operations
│   ├── dir.c               # Directory operations
│   ├── extents.c           # Extent management
│   ├── file.c              # File operations
│   ├── fsync.c             # File synchronization
│   ├── hash.c              # Hash functions
│   ├── ialloc.c            # Inode allocation
│   ├── indirect.c          # Indirect blocks
│   ├── inline.c            # Inline data
│   ├── inode.c             # Inode operations
│   ├── ioctl.c             # I/O control
│   ├── mballoc.c           # Multi-block allocation
│   ├── migrate.c           # Migration support
│   ├── mmp.c               # Multiple mount protection
│   ├── namei.c             # Name lookup
│   ├── page-io.c           # Page I/O
│   ├── readpage.c          # Page reading
│   ├── resize.c            # File system resize
│   ├── super.c             # Super block operations
│   ├── symlink.c           # Symbolic links
│   ├── sysfs.c             # Sysfs interface
│   └── xattr.c             # Extended attributes
├── <strong>btrfs/</strong>               # Btrfs file system
│   ├── ctree.c             # B-tree operations
│   ├── disk-io.c           # Disk I/O
│   ├── extent_io.c         # Extent I/O
│   ├── file.c              # File operations
│   ├── inode.c             # Inode operations
│   ├── super.c             # Super block operations
│   ├── transaction.c       # Transaction management
│   ├── volumes.c           # Volume management
│   └── compression.c       # Compression support
├── <strong>xfs/</strong>                 # XFS file system
│   ├── xfs_alloc.c         # Allocation
│   ├── xfs_bmap.c          # Block mapping
│   ├── xfs_dir2.c          # Directory operations
│   ├── xfs_file.c          # File operations
│   ├── xfs_inode.c         # Inode operations
│   ├── xfs_log.c           # Logging
│   ├── xfs_mount.c         # Mount operations
│   └── xfs_super.c         # Super block operations
├── <strong>proc/</strong>                # /proc file system
│   ├── base.c              # Process directories
│   ├── generic.c           # Generic proc operations
│   ├── inode.c             # Proc inodes
│   ├── root.c              # Root directory
│   ├── stat.c              # Statistics
│   └── task_mmu.c          # Memory mapping info
├── <strong>sysfs/</strong>               # Sysfs file system
│   ├── dir.c               # Directory operations
│   ├── file.c              # File operations
│   ├── group.c             # Attribute groups
│   ├── inode.c             # Inode operations
│   ├── mount.c             # Mount operations
│   └── symlink.c           # Symbolic links
├── <strong>debugfs/</strong>             # Debug file system
│   ├── inode.c             # Inode operations
│   └── file.c              # File operations
├── <strong>ramfs/</strong>               # RAM file system
│   ├── inode.c             # Inode operations
│   └── file-mmu.c          # MMU file operations
├── <strong>hugetlbfs/</strong>           # Huge page file system
│   └── inode.c             # Inode operations
├── <strong>configfs/</strong>            # Configuration file system
│   ├── configfs_internal.h # Internal definitions
│   ├── dir.c               # Directory operations
│   ├── file.c              # File operations
│   ├── inode.c             # Inode operations
│   ├── item.c              # Configuration items
│   ├── mount.c             # Mount operations
│   └── symlink.c           # Symbolic links
├── <strong>autofs/</strong>              # Auto-mounting file system
│   ├── dev-ioctl.c         # Device I/O control
│   ├── dirhash.c           # Directory hashing
│   ├── expire.c            # Expiration handling
│   ├── inode.c             # Inode operations
│   ├── root.c              # Root operations
│   └── waitq.c             # Wait queue operations
├── <strong>nfs/</strong>                 # Network File System
│   ├── client.c            # NFS client
│   ├── dir.c               # Directory operations
│   ├── file.c              # File operations
│   ├── inode.c             # Inode operations
│   ├── nfs4proc.c          # NFSv4 procedures
│   ├── read.c              # Read operations
│   ├── write.c             # Write operations
│   └── super.c             # Super block operations
├── <strong>nfsd/</strong>                # NFS daemon
│   ├── nfssvc.c            # NFS service
│   ├── nfsproc.c           # NFS procedures
│   ├── nfs4proc.c          # NFSv4 procedures
│   ├── vfs.c               # VFS interface
│   └── export.c            # Export handling
├── <strong>cifs/</strong>                # Common Internet File System
│   ├── cifsfs.c            # CIFS file system
│   ├── connect.c           # Connection handling
│   ├── dir.c               # Directory operations
│   ├── file.c              # File operations
│   ├── inode.c             # Inode operations
│   └── transport.c         # Transport layer
├── <strong>fat/</strong>                 # FAT file system
│   ├── cache.c             # Cache operations
│   ├── dir.c               # Directory operations
│   ├── fatent.c            # FAT entry operations
│   ├── file.c              # File operations
│   ├── inode.c             # Inode operations
│   ├── misc.c              # Miscellaneous
│   └── namei.c             # Name lookup
├── <strong>ntfs/</strong>                # NTFS file system
│   ├── aops.c              # Address space operations
│   ├── attrib.c            # Attribute operations
│   ├── compress.c          # Compression
│   ├── dir.c               # Directory operations
│   ├── file.c              # File operations
│   ├── inode.c             # Inode operations
│   ├── mft.c               # Master file table
│   └── super.c             # Super block operations
├── <strong>isofs/</strong>               # ISO 9660 file system
│   ├── dir.c               # Directory operations
│   ├── file.c              # File operations
│   ├── inode.c             # Inode operations
│   ├── namei.c             # Name lookup
│   └── rock.c              # Rock Ridge extensions
├── <strong>udf/</strong>                 # Universal Disk Format
│   ├── balloc.c            # Block allocation
│   ├── dir.c               # Directory operations
│   ├── file.c              # File operations
│   ├── ialloc.c            # Inode allocation
│   ├── inode.c             # Inode operations
│   ├── namei.c             # Name lookup
│   ├── partition.c         # Partition handling
│   ├── super.c             # Super block operations
│   └── symlink.c           # Symbolic links
├── <strong>squashfs/</strong>            # Squash file system
│   ├── block.c             # Block operations
│   ├── cache.c             # Cache operations
│   ├── dir.c               # Directory operations
│   ├── export.c            # Export operations
│   ├── file.c              # File operations
│   ├── fragment.c          # Fragment handling
│   ├── id.c                # ID handling
│   ├── inode.c             # Inode operations
│   ├── namei.c             # Name lookup
│   ├── super.c             # Super block operations
│   ├── symlink.c           # Symbolic links
│   └── xattr.c             # Extended attributes
├── <strong>overlayfs/</strong>           # Overlay file system
│   ├── copy_up.c           # Copy-up operations
│   ├── dir.c               # Directory operations
│   ├── export.c            # Export operations
│   ├── file.c              # File operations
│   ├── inode.c             # Inode operations
│   ├── namei.c             # Name lookup
│   ├── overlayfs.h         # Header file
│   ├── readdir.c           # Directory reading
│   ├── super.c             # Super block operations
│   └── util.c              # Utility functions
├── <strong>fuse/</strong>                # Filesystem in Userspace
│   ├── control.c           # Control interface
│   ├── cuse.c              # Character device in userspace
│   ├── dev.c               # Device operations
│   ├── dir.c               # Directory operations
│   ├── file.c              # File operations
│   ├── fuse_i.h            # Internal header
│   ├── inode.c             # Inode operations
│   └── virtio_fs.c         # VirtIO file system
├── <strong>ecryptfs/</strong>            # Encrypted file system
│   ├── crypto.c            # Cryptographic operations
│   ├── dentry.c            # Dentry operations
│   ├── file.c              # File operations
│   ├── inode.c             # Inode operations
│   ├── keystore.c          # Key storage
│   ├── kthread.c           # Kernel thread
│   ├── main.c              # Main operations
│   ├── messaging.c         # Messaging
│   ├── miscdev.c           # Miscellaneous device
│   ├── mmap.c              # Memory mapping
│   └── super.c             # Super block operations
├── <strong>jffs2/</strong>               # Journalling Flash File System v2
│   ├── build.c             # Build operations
│   ├── compr.c             # Compression
│   ├── debug.c             # Debug operations
│   ├── dir.c               # Directory operations
│   ├── erase.c             # Erase operations
│   ├── file.c              # File operations
│   ├── fs.c                # File system operations
│   ├── gc.c                # Garbage collection
│   ├── malloc.c            # Memory allocation
│   ├── nodelist.c          # Node list operations
│   ├── nodemgmt.c          # Node management
│   ├── read.c              # Read operations
│   ├── readinode.c         # Inode reading
│   ├── scan.c              # Scanning operations
│   ├── super.c             # Super block operations
│   ├── symlink.c           # Symbolic links
│   ├── wbuf.c              # Write buffer
│   ├── write.c             # Write operations
│   └── xattr.c             # Extended attributes
├── <strong>ubifs/</strong>               # Unsorted Block Image File System
│   ├── budget.c            # Budget management
│   ├── commit.c            # Commit operations
│   ├── compress.c          # Compression
│   ├── debug.c             # Debug operations
│   ├── dir.c               # Directory operations
│   ├── file.c              # File operations
│   ├── find.c              # Find operations
│   ├── gc.c                # Garbage collection
│   ├── io.c                # I/O operations
│   ├── journal.c           # Journal operations
│   ├── log.c               # Logging
│   ├── lprops.c            # LEB properties
│   ├── lpt.c               # LEB property tree
│   ├── lpt_commit.c        # LPT commit
│   ├── master.c            # Master operations
│   ├── misc.c              # Miscellaneous
│   ├── orphan.c            # Orphan handling
│   ├── recovery.c          # Recovery operations
│   ├── replay.c            # Replay operations
│   ├── sb.c                # Super block operations
│   ├── scan.c              # Scanning operations
│   ├── super.c             # Super operations
│   ├── tnc.c               # Tree node cache
│   ├── tnc_commit.c        # TNC commit
│   ├── tnc_misc.c          # TNC miscellaneous
│   └── xattr.c             # Extended attributes
└── <strong>quota/</strong>               # Disk quota
    ├── dquot.c             # Disk quota operations
    ├── quota.c             # Quota operations
    ├── quota_tree.c        # Quota tree
    ├── quota_v1.c          # Quota version 1
    └── quota_v2.c          # Quota version 2
                        </pre>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/search.js"></script>
    <script src="../../assets/js/syntax.js"></script>
</body>
</html>
