/**
 * Syntax Highlighting for Linux Kernel Documentation
 * Enhanced highlighting for C code with kernel-specific patterns
 */

class KernelSyntaxHighlighter {
  constructor() {
    this.kernelKeywords = new Set([
      // Kernel-specific keywords
      'EXPORT_SYMBOL', 'EXPORT_SYMBOL_GPL', 'MODULE_LICENSE', 'MODULE_AUTHOR',
      'MODULE_DESCRIPTION', 'MODULE_VERSION', 'module_init', 'module_exit',
      'SYSCALL_DEFINE0', 'SYSCALL_DEFINE1', 'SYSCALL_DEFINE2', 'SYSCALL_DEFINE3',
      'SYSCALL_DEFINE4', 'SYSCALL_DEFINE5', 'SYSCALL_DEFINE6',
      'asmlinkage', '__init', '__exit', '__initdata', '__exitdata',
      '__user', '__kernel', '__iomem', '__percpu', '__rcu',
      'likely', 'unlikely', 'barrier', 'smp_mb', 'smp_rmb', 'smp_wmb',
      'BUG', 'BUG_ON', 'WARN', 'WARN_ON', 'WARN_ONCE',
      'pr_debug', 'pr_info', 'pr_warn', 'pr_err', 'pr_crit',
      'printk', 'KERN_DEBUG', 'KERN_INFO', 'KERN_NOTICE', 'KERN_WARNING',
      'KERN_ERR', 'KERN_CRIT', 'KERN_ALERT', 'KERN_EMERG'
    ]);

    this.kernelTypes = new Set([
      // Common kernel types
      'u8', 'u16', 'u32', 'u64', 's8', 's16', 's32', 's64',
      'uint8_t', 'uint16_t', 'uint32_t', 'uint64_t',
      'int8_t', 'int16_t', 'int32_t', 'int64_t',
      'size_t', 'ssize_t', 'off_t', 'loff_t', 'pid_t', 'uid_t', 'gid_t',
      'dev_t', 'ino_t', 'mode_t', 'nlink_t', 'time_t',
      'atomic_t', 'atomic64_t', 'spinlock_t', 'mutex', 'semaphore',
      'wait_queue_head_t', 'completion', 'work_struct', 'delayed_work',
      'timer_list', 'tasklet_struct', 'irqreturn_t',
      'gfp_t', 'dma_addr_t', 'phys_addr_t', 'resource_size_t',
      'struct page', 'struct inode', 'struct file', 'struct dentry',
      'struct super_block', 'struct address_space', 'struct vm_area_struct',
      'struct task_struct', 'struct mm_struct', 'struct pt_regs',
      'struct device', 'struct platform_device', 'struct pci_dev',
      'struct net_device', 'struct sk_buff', 'struct socket'
    ]);

    this.kernelMacros = new Set([
      // Common kernel macros
      'GFP_KERNEL', 'GFP_ATOMIC', 'GFP_USER', 'GFP_HIGHUSER', 'GFP_NOIO', 'GFP_NOFS',
      'SLAB_HWCACHE_ALIGN', 'SLAB_CACHE_DMA', 'SLAB_PANIC',
      'PAGE_SIZE', 'PAGE_SHIFT', 'PAGE_MASK', 'PAGE_ALIGN',
      'TASK_RUNNING', 'TASK_INTERRUPTIBLE', 'TASK_UNINTERRUPTIBLE',
      'TASK_STOPPED', 'TASK_TRACED', 'TASK_DEAD',
      'CLONE_VM', 'CLONE_FS', 'CLONE_FILES', 'CLONE_SIGHAND', 'CLONE_THREAD',
      'O_RDONLY', 'O_WRONLY', 'O_RDWR', 'O_CREAT', 'O_EXCL', 'O_TRUNC',
      'PROT_READ', 'PROT_WRITE', 'PROT_EXEC', 'PROT_NONE',
      'MAP_SHARED', 'MAP_PRIVATE', 'MAP_ANONYMOUS', 'MAP_FIXED',
      'SIGKILL', 'SIGTERM', 'SIGINT', 'SIGUSR1', 'SIGUSR2',
      'EPERM', 'ENOENT', 'ESRCH', 'EINTR', 'EIO', 'ENXIO', 'E2BIG',
      'ENOEXEC', 'EBADF', 'ECHILD', 'EAGAIN', 'ENOMEM', 'EACCES'
    ]);

    this.kernelFunctions = new Set([
      // Common kernel functions
      'kmalloc', 'kzalloc', 'kfree', 'vmalloc', 'vfree', 'kcalloc',
      'kmem_cache_alloc', 'kmem_cache_free', 'kmem_cache_create',
      'alloc_pages', 'free_pages', 'get_free_page', 'free_page',
      'copy_from_user', 'copy_to_user', 'get_user', 'put_user',
      'request_irq', 'free_irq', 'disable_irq', 'enable_irq',
      'spin_lock', 'spin_unlock', 'spin_lock_irqsave', 'spin_unlock_irqrestore',
      'mutex_lock', 'mutex_unlock', 'mutex_trylock',
      'down', 'up', 'down_interruptible', 'down_trylock',
      'wake_up', 'wake_up_interruptible', 'wait_event', 'wait_event_interruptible',
      'schedule', 'yield', 'msleep', 'ssleep', 'udelay', 'mdelay',
      'jiffies_to_msecs', 'msecs_to_jiffies', 'time_after', 'time_before',
      'ioremap', 'iounmap', 'readb', 'readw', 'readl', 'writeb', 'writew', 'writel',
      'register_chrdev', 'unregister_chrdev', 'alloc_chrdev_region',
      'cdev_init', 'cdev_add', 'cdev_del'
    ]);

    this.init();
  }

  init() {
    // Initialize syntax highlighting for existing code blocks
    this.highlightAll();
    
    // Set up observer for dynamically added code blocks
    this.setupObserver();
  }

  highlightAll() {
    const codeBlocks = document.querySelectorAll('pre code:not(.hljs)');
    codeBlocks.forEach(block => this.highlightBlock(block));
  }

  highlightBlock(block) {
    const code = block.textContent;
    const highlighted = this.highlightCode(code);
    block.innerHTML = highlighted;
    block.classList.add('hljs', 'hljs-c');
  }

  highlightCode(code) {
    // First, escape HTML
    let highlighted = this.escapeHtml(code);
    
    // Apply syntax highlighting
    highlighted = this.highlightComments(highlighted);
    highlighted = this.highlightStrings(highlighted);
    highlighted = this.highlightNumbers(highlighted);
    highlighted = this.highlightPreprocessor(highlighted);
    highlighted = this.highlightKernelKeywords(highlighted);
    highlighted = this.highlightKernelTypes(highlighted);
    highlighted = this.highlightKernelMacros(highlighted);
    highlighted = this.highlightKernelFunctions(highlighted);
    highlighted = this.highlightCKeywords(highlighted);
    highlighted = this.highlightOperators(highlighted);
    
    return highlighted;
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  highlightComments(code) {
    // Single line comments
    code = code.replace(/\/\/.*$/gm, '<span class="hljs-comment">$&</span>');
    
    // Multi-line comments
    code = code.replace(/\/\*[\s\S]*?\*\//g, '<span class="hljs-comment">$&</span>');
    
    return code;
  }

  highlightStrings(code) {
    // Double quoted strings
    code = code.replace(/"(?:[^"\\]|\\.)*"/g, '<span class="hljs-string">$&</span>');
    
    // Single quoted strings (character literals)
    code = code.replace(/'(?:[^'\\]|\\.)*'/g, '<span class="hljs-string">$&</span>');
    
    return code;
  }

  highlightNumbers(code) {
    // Hexadecimal numbers
    code = code.replace(/\b0[xX][0-9a-fA-F]+[uUlL]*\b/g, '<span class="hljs-number">$&</span>');
    
    // Decimal numbers
    code = code.replace(/\b\d+\.?\d*[fFlL]?\b/g, '<span class="hljs-number">$&</span>');
    
    return code;
  }

  highlightPreprocessor(code) {
    // Preprocessor directives
    code = code.replace(/^#.*$/gm, '<span class="hljs-meta">$&</span>');
    
    return code;
  }

  highlightKernelKeywords(code) {
    const pattern = new RegExp(`\\b(${Array.from(this.kernelKeywords).join('|')})\\b`, 'g');
    return code.replace(pattern, '<span class="kernel-macro">$1</span>');
  }

  highlightKernelTypes(code) {
    const pattern = new RegExp(`\\b(${Array.from(this.kernelTypes).join('|')})\\b`, 'g');
    return code.replace(pattern, '<span class="kernel-type">$1</span>');
  }

  highlightKernelMacros(code) {
    const pattern = new RegExp(`\\b(${Array.from(this.kernelMacros).join('|')})\\b`, 'g');
    return code.replace(pattern, '<span class="kernel-constant">$1</span>');
  }

  highlightKernelFunctions(code) {
    const pattern = new RegExp(`\\b(${Array.from(this.kernelFunctions).join('|')})\\b`, 'g');
    return code.replace(pattern, '<span class="kernel-function">$1</span>');
  }

  highlightCKeywords(code) {
    const cKeywords = [
      'auto', 'break', 'case', 'char', 'const', 'continue', 'default', 'do',
      'double', 'else', 'enum', 'extern', 'float', 'for', 'goto', 'if',
      'inline', 'int', 'long', 'register', 'restrict', 'return', 'short',
      'signed', 'sizeof', 'static', 'struct', 'switch', 'typedef', 'union',
      'unsigned', 'void', 'volatile', 'while', '_Bool', '_Complex', '_Imaginary'
    ];
    
    const pattern = new RegExp(`\\b(${cKeywords.join('|')})\\b`, 'g');
    return code.replace(pattern, '<span class="hljs-keyword">$1</span>');
  }

  highlightOperators(code) {
    const operators = [
      '\\+\\+', '--', '\\+=', '-=', '\\*=', '/=', '%=', '&=', '\\|=', '\\^=',
      '<<=', '>>=', '==', '!=', '<=', '>=', '&&', '\\|\\|', '<<', '>>',
      '\\+', '-', '\\*', '/', '%', '&', '\\|', '\\^', '~', '!', '<', '>',
      '=', '\\?', ':'
    ];
    
    const pattern = new RegExp(`(${operators.join('|')})`, 'g');
    return code.replace(pattern, '<span class="hljs-operator">$1</span>');
  }

  setupObserver() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const codeBlocks = node.querySelectorAll ? 
              node.querySelectorAll('pre code:not(.hljs)') : [];
            codeBlocks.forEach(block => this.highlightBlock(block));
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // Add line numbers to code blocks
  addLineNumbers(block) {
    const lines = block.innerHTML.split('\n');
    const numberedLines = lines.map((line, index) => {
      const lineNumber = index + 1;
      return `<span class="hljs-ln-line">` +
             `<span class="hljs-ln-numbers">${lineNumber}</span>` +
             `<span class="hljs-ln-code">${line}</span>` +
             `</span>`;
    });
    
    block.innerHTML = numberedLines.join('\n');
    block.classList.add('hljs-ln');
  }

  // Highlight specific kernel constructs
  highlightKernelConstructs(code) {
    // System calls
    code = code.replace(/\bSYSCALL_DEFINE\d+\s*\([^)]*\)/g, 
      '<span class="syscall">$&</span>');
    
    // Interrupt handlers
    code = code.replace(/\b\w+_interrupt\b/g, 
      '<span class="interrupt-handler">$&</span>');
    
    // Module parameters
    code = code.replace(/\bmodule_param\s*\([^)]*\)/g, 
      '<span class="module-param">$&</span>');
    
    // Configuration options
    code = code.replace(/\bCONFIG_\w+\b/g, 
      '<span class="config-option">$&</span>');
    
    return code;
  }

  // Enhanced highlighting for specific file types
  highlightByFileType(code, filename) {
    if (filename.endsWith('.h')) {
      // Header file specific highlighting
      code = this.highlightHeaderFile(code);
    } else if (filename.includes('Makefile') || filename.includes('Kconfig')) {
      // Build system files
      code = this.highlightBuildFile(code);
    }
    
    return code;
  }

  highlightHeaderFile(code) {
    // Include guards
    code = code.replace(/^#ifndef\s+\w+$|^#define\s+\w+$|^#endif$/gm,
      '<span class="hljs-meta">$&</span>');
    
    // Function declarations
    code = code.replace(/^\s*\w+\s+\*?\w+\s*\([^)]*\)\s*;/gm,
      '<span class="hljs-function">$&</span>');
    
    return code;
  }

  highlightBuildFile(code) {
    // Makefile variables
    code = code.replace(/^\w+\s*[:+]?=/gm,
      '<span class="hljs-variable">$&</span>');
    
    // Kconfig options
    code = code.replace(/^\s*(config|menuconfig|choice|endchoice)\s+/gm,
      '<span class="hljs-keyword">$1</span> ');
    
    return code;
  }
}

// Initialize syntax highlighter when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new KernelSyntaxHighlighter();
});
