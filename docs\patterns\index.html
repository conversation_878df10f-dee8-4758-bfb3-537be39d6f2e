<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Linux kernel coding patterns, best practices, and programming idioms">
    <meta name="keywords" content="Linux kernel, coding patterns, best practices, programming idioms, kernel development">
    
    <title>Kernel Coding Patterns - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐧</text></svg>">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle" aria-expanded="false">▶</button>
                    <a href="../subsystems/index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="../subsystems/mm/index.html" class="nav-link">💾 Memory Management</a>
                        </li>
                        <li class="nav-item">
                            <a href="#kernel" class="nav-link">⚙️ Core Kernel - Coming Soon</a>
                        </li>
                        <li class="nav-item">
                            <a href="#fs" class="nav-link">📁 File Systems - Coming Soon</a>
                        </li>
                        <li class="nav-item">
                            <a href="#net" class="nav-link">🌐 Networking - Coming Soon</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="index.html" class="nav-link active">🎯 Coding Patterns</a>
                </li>
                
                <li class="nav-item">
                    <a href="../api/index.html" class="nav-link">📚 API Reference</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="../index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <span>Coding Patterns</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>🎯 Linux Kernel Coding Patterns</h1>
                </div>
                <div class="section-content">
                    <p>
                        This section documents common coding patterns, idioms, and best practices used 
                        throughout the Linux kernel. Understanding these patterns is essential for 
                        effective kernel development and code review.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">📋 Pattern Categories</h3>
                        </div>
                        <ul>
                            <li><strong>Memory Management:</strong> Allocation, deallocation, and reference counting</li>
                            <li><strong>Synchronization:</strong> Locking patterns and atomic operations</li>
                            <li><strong>Error Handling:</strong> Consistent error reporting and cleanup</li>
                            <li><strong>Module Patterns:</strong> Initialization, cleanup, and parameter handling</li>
                            <li><strong>Device Drivers:</strong> Common driver framework patterns</li>
                            <li><strong>Data Structures:</strong> Kernel-specific data structure usage</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Memory Management Patterns -->
            <section class="content-section">
                <div class="section-header">
                    <h2>💾 Memory Management Patterns</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="pattern-section">
                        <h3>Pattern: Safe Memory Allocation</h3>
                        <p><strong>Problem:</strong> Memory allocation can fail, leading to kernel panics or undefined behavior.</p>
                        <p><strong>Solution:</strong> Always check allocation return values and handle failures gracefully.</p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* ❌ Bad: No error checking */
void *buffer = kmalloc(size, GFP_KERNEL);
strcpy(buffer, source); /* Potential NULL pointer dereference */

/* ✅ Good: Proper error checking */
void *buffer = kmalloc(size, GFP_KERNEL);
if (!buffer) {
    pr_err("Failed to allocate %zu bytes\n", size);
    return -ENOMEM;
}

/* Use buffer safely */
strcpy(buffer, source);

/* Always clean up */
kfree(buffer);
buffer = NULL; /* Prevent accidental reuse */</code></pre>
                        </div>
                    </div>

                    <div class="pattern-section">
                        <h3>Pattern: Reference Counting</h3>
                        <p><strong>Problem:</strong> Shared objects need to be freed when no longer referenced.</p>
                        <p><strong>Solution:</strong> Use atomic reference counting with get/put semantics.</p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct my_object {
    atomic_t refcount;
    /* other fields */
};

/* Initialize reference count */
static struct my_object *my_object_alloc(void)
{
    struct my_object *obj = kzalloc(sizeof(*obj), GFP_KERNEL);
    if (obj)
        atomic_set(&obj->refcount, 1);
    return obj;
}

/* Get a reference */
static struct my_object *my_object_get(struct my_object *obj)
{
    if (obj)
        atomic_inc(&obj->refcount);
    return obj;
}

/* Release a reference */
static void my_object_put(struct my_object *obj)
{
    if (obj && atomic_dec_and_test(&obj->refcount)) {
        /* Last reference - free the object */
        kfree(obj);
    }
}</code></pre>
                        </div>
                    </div>

                    <div class="pattern-section">
                        <h3>Pattern: RAII-style Resource Management</h3>
                        <p><strong>Problem:</strong> Resources need to be cleaned up on all exit paths.</p>
                        <p><strong>Solution:</strong> Use goto-based cleanup with single exit point.</p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>int complex_function(void)
{
    void *buffer1 = NULL;
    void *buffer2 = NULL;
    struct file *file = NULL;
    int ret = 0;

    buffer1 = kmalloc(SIZE1, GFP_KERNEL);
    if (!buffer1) {
        ret = -ENOMEM;
        goto cleanup;
    }

    buffer2 = kmalloc(SIZE2, GFP_KERNEL);
    if (!buffer2) {
        ret = -ENOMEM;
        goto cleanup;
    }

    file = filp_open("/path/to/file", O_RDONLY, 0);
    if (IS_ERR(file)) {
        ret = PTR_ERR(file);
        file = NULL;
        goto cleanup;
    }

    /* Do work... */
    ret = do_work(buffer1, buffer2, file);

cleanup:
    if (file)
        filp_close(file, NULL);
    kfree(buffer2);
    kfree(buffer1);
    return ret;
}</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Synchronization Patterns -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔒 Synchronization Patterns</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="pattern-section">
                        <h3>Pattern: Spinlock Usage</h3>
                        <p><strong>Problem:</strong> Protecting critical sections in interrupt context.</p>
                        <p><strong>Solution:</strong> Use spinlocks with proper interrupt handling.</p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>static DEFINE_SPINLOCK(my_lock);

/* In process context */
void process_context_function(void)
{
    unsigned long flags;
    
    spin_lock_irqsave(&my_lock, flags);
    /* Critical section - interrupts disabled */
    modify_shared_data();
    spin_unlock_irqrestore(&my_lock, flags);
}

/* In interrupt context */
irqreturn_t my_interrupt_handler(int irq, void *dev_id)
{
    spin_lock(&my_lock);
    /* Critical section - already in interrupt context */
    modify_shared_data();
    spin_unlock(&my_lock);
    
    return IRQ_HANDLED;
}</code></pre>
                        </div>
                    </div>

                    <div class="pattern-section">
                        <h3>Pattern: Mutex for Sleeping Context</h3>
                        <p><strong>Problem:</strong> Need to protect resources that may require sleeping.</p>
                        <p><strong>Solution:</strong> Use mutexes in process context where sleeping is allowed.</p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>static DEFINE_MUTEX(my_mutex);

int function_that_may_sleep(void)
{
    int ret;
    
    ret = mutex_lock_interruptible(&my_mutex);
    if (ret)
        return ret; /* Interrupted by signal */
    
    /* Critical section - can sleep here */
    ret = copy_from_user(kernel_buffer, user_buffer, size);
    if (ret) {
        ret = -EFAULT;
        goto unlock;
    }
    
    /* More work that might sleep */
    ret = some_blocking_operation();
    
unlock:
    mutex_unlock(&my_mutex);
    return ret;
}</code></pre>
                        </div>
                    </div>

                    <div class="pattern-section">
                        <h3>Pattern: RCU (Read-Copy-Update)</h3>
                        <p><strong>Problem:</strong> High-performance read-mostly data structures.</p>
                        <p><strong>Solution:</strong> Use RCU for lockless reads with synchronized updates.</p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct my_data {
    int value;
    struct rcu_head rcu;
};

static struct my_data __rcu *global_data;
static DEFINE_SPINLOCK(update_lock);

/* RCU reader */
int read_data(void)
{
    struct my_data *data;
    int value;
    
    rcu_read_lock();
    data = rcu_dereference(global_data);
    if (data)
        value = data->value;
    else
        value = -1;
    rcu_read_unlock();
    
    return value;
}

/* RCU updater */
void update_data(int new_value)
{
    struct my_data *new_data, *old_data;
    
    new_data = kmalloc(sizeof(*new_data), GFP_KERNEL);
    if (!new_data)
        return;
    
    new_data->value = new_value;
    
    spin_lock(&update_lock);
    old_data = rcu_dereference_protected(global_data,
                                        lockdep_is_held(&update_lock));
    rcu_assign_pointer(global_data, new_data);
    spin_unlock(&update_lock);
    
    if (old_data)
        kfree_rcu(old_data, rcu);
}</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Error Handling Patterns -->
            <section class="content-section">
                <div class="section-header">
                    <h2>⚠️ Error Handling Patterns</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="pattern-section">
                        <h3>Pattern: Consistent Error Codes</h3>
                        <p><strong>Problem:</strong> Inconsistent error reporting makes debugging difficult.</p>
                        <p><strong>Solution:</strong> Use standard errno values and consistent error handling.</p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* Standard error codes */
int my_function(struct device *dev, void __user *arg)
{
    if (!dev)
        return -EINVAL;  /* Invalid argument */
    
    if (!capable(CAP_SYS_ADMIN))
        return -EPERM;   /* Permission denied */
    
    void *buffer = kmalloc(1024, GFP_KERNEL);
    if (!buffer)
        return -ENOMEM;  /* Out of memory */
    
    if (copy_from_user(buffer, arg, 1024)) {
        kfree(buffer);
        return -EFAULT;  /* Bad address */
    }
    
    int ret = hardware_operation(dev, buffer);
    kfree(buffer);
    
    if (ret < 0)
        return -EIO;     /* I/O error */
    
    return 0; /* Success */
}</code></pre>
                        </div>
                    </div>

                    <div class="pattern-section">
                        <h3>Pattern: Error Logging</h3>
                        <p><strong>Problem:</strong> Need consistent and informative error messages.</p>
                        <p><strong>Solution:</strong> Use kernel logging functions with appropriate levels.</p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* Use appropriate log levels */
int init_hardware(struct pci_dev *pdev)
{
    int ret;
    
    ret = pci_enable_device(pdev);
    if (ret) {
        dev_err(&pdev->dev, "Failed to enable PCI device: %d\n", ret);
        return ret;
    }
    
    ret = request_irq(pdev->irq, my_interrupt, IRQF_SHARED, 
                      "my_driver", pdev);
    if (ret) {
        dev_err(&pdev->dev, "Failed to request IRQ %d: %d\n", 
                pdev->irq, ret);
        goto disable_device;
    }
    
    dev_info(&pdev->dev, "Hardware initialized successfully\n");
    return 0;
    
disable_device:
    pci_disable_device(pdev);
    return ret;
}</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Module Patterns -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📦 Module Patterns</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="pattern-section">
                        <h3>Pattern: Module Initialization and Cleanup</h3>
                        <p><strong>Problem:</strong> Modules need proper initialization and cleanup sequences.</p>
                        <p><strong>Solution:</strong> Use standard module init/exit patterns with proper error handling.</p>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>static int __init my_module_init(void)
{
    int ret;

    pr_info("Loading my_module\n");

    /* Initialize resources in order */
    ret = register_chrdev_region(MKDEV(MAJOR_NUM, 0), 1, "my_device");
    if (ret) {
        pr_err("Failed to register char device region: %d\n", ret);
        return ret;
    }

    cdev_init(&my_cdev, &my_fops);
    ret = cdev_add(&my_cdev, MKDEV(MAJOR_NUM, 0), 1);
    if (ret) {
        pr_err("Failed to add char device: %d\n", ret);
        goto unregister_region;
    }

    my_class = class_create(THIS_MODULE, "my_class");
    if (IS_ERR(my_class)) {
        ret = PTR_ERR(my_class);
        pr_err("Failed to create device class: %d\n", ret);
        goto del_cdev;
    }

    pr_info("my_module loaded successfully\n");
    return 0;

    /* Cleanup in reverse order */
del_cdev:
    cdev_del(&my_cdev);
unregister_region:
    unregister_chrdev_region(MKDEV(MAJOR_NUM, 0), 1);
    return ret;
}

static void __exit my_module_exit(void)
{
    pr_info("Unloading my_module\n");

    /* Cleanup in reverse order of initialization */
    class_destroy(my_class);
    cdev_del(&my_cdev);
    unregister_chrdev_region(MKDEV(MAJOR_NUM, 0), 1);

    pr_info("my_module unloaded\n");
}

module_init(my_module_init);
module_exit(my_module_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("Your Name");
MODULE_DESCRIPTION("Example kernel module");
MODULE_VERSION("1.0");</code></pre>
                        </div>
                    </div>

                    <div class="pattern-section">
                        <h3>Pattern: Module Parameters</h3>
                        <p><strong>Problem:</strong> Need configurable module behavior.</p>
                        <p><strong>Solution:</strong> Use module parameters with proper validation.</p>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* Module parameters */
static int debug = 0;
module_param(debug, int, 0644);
MODULE_PARM_DESC(debug, "Enable debug output (0=off, 1=on)");

static char *device_name = "mydevice";
module_param(device_name, charp, 0644);
MODULE_PARM_DESC(device_name, "Device name to use");

static int buffer_size = 1024;
module_param(buffer_size, int, 0644);
MODULE_PARM_DESC(buffer_size, "Buffer size in bytes (min=512, max=4096)");

/* Parameter validation */
static int validate_parameters(void)
{
    if (buffer_size < 512 || buffer_size > 4096) {
        pr_err("Invalid buffer_size %d (must be 512-4096)\n", buffer_size);
        return -EINVAL;
    }

    if (!device_name || strlen(device_name) == 0) {
        pr_err("Invalid device_name\n");
        return -EINVAL;
    }

    return 0;
}

static int __init my_module_init(void)
{
    int ret = validate_parameters();
    if (ret)
        return ret;

    if (debug)
        pr_info("Debug mode enabled\n");

    /* Continue with initialization... */
    return 0;
}</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Device Driver Patterns -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔌 Device Driver Patterns</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="pattern-section">
                        <h3>Pattern: Platform Driver Structure</h3>
                        <p><strong>Problem:</strong> Need consistent platform driver implementation.</p>
                        <p><strong>Solution:</strong> Use standard platform driver framework.</p>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>static int my_probe(struct platform_device *pdev)
{
    struct device *dev = &pdev->dev;
    struct my_device_data *data;
    struct resource *res;
    int ret;

    /* Allocate device data */
    data = devm_kzalloc(dev, sizeof(*data), GFP_KERNEL);
    if (!data)
        return -ENOMEM;

    /* Get resources */
    res = platform_get_resource(pdev, IORESOURCE_MEM, 0);
    data->base = devm_ioremap_resource(dev, res);
    if (IS_ERR(data->base))
        return PTR_ERR(data->base);

    data->irq = platform_get_irq(pdev, 0);
    if (data->irq < 0)
        return data->irq;

    /* Request IRQ */
    ret = devm_request_irq(dev, data->irq, my_irq_handler,
                          0, dev_name(dev), data);
    if (ret) {
        dev_err(dev, "Failed to request IRQ: %d\n", ret);
        return ret;
    }

    /* Store device data */
    platform_set_drvdata(pdev, data);

    dev_info(dev, "Device probed successfully\n");
    return 0;
}

static int my_remove(struct platform_device *pdev)
{
    struct my_device_data *data = platform_get_drvdata(pdev);

    /* Cleanup is automatic with devm_* functions */
    dev_info(&pdev->dev, "Device removed\n");
    return 0;
}

static const struct of_device_id my_of_match[] = {
    { .compatible = "vendor,my-device" },
    { /* sentinel */ }
};
MODULE_DEVICE_TABLE(of, my_of_match);

static struct platform_driver my_driver = {
    .probe = my_probe,
    .remove = my_remove,
    .driver = {
        .name = "my-device",
        .of_match_table = my_of_match,
    },
};

module_platform_driver(my_driver);</code></pre>
                        </div>
                    </div>

                    <div class="pattern-section">
                        <h3>Pattern: Character Device Operations</h3>
                        <p><strong>Problem:</strong> Need to implement character device file operations.</p>
                        <p><strong>Solution:</strong> Use standard file operations structure with proper error handling.</p>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>static int my_open(struct inode *inode, struct file *file)
{
    struct my_device_data *data;

    data = container_of(inode->i_cdev, struct my_device_data, cdev);
    file->private_data = data;

    /* Initialize per-file state if needed */
    return nonseekable_open(inode, file);
}

static int my_release(struct inode *inode, struct file *file)
{
    /* Cleanup per-file state */
    return 0;
}

static ssize_t my_read(struct file *file, char __user *buf,
                       size_t count, loff_t *ppos)
{
    struct my_device_data *data = file->private_data;
    ssize_t ret;

    if (mutex_lock_interruptible(&data->mutex))
        return -ERESTARTSYS;

    /* Read data from device */
    ret = read_from_hardware(data, buf, count);

    mutex_unlock(&data->mutex);
    return ret;
}

static ssize_t my_write(struct file *file, const char __user *buf,
                        size_t count, loff_t *ppos)
{
    struct my_device_data *data = file->private_data;
    ssize_t ret;

    if (mutex_lock_interruptible(&data->mutex))
        return -ERESTARTSYS;

    /* Write data to device */
    ret = write_to_hardware(data, buf, count);

    mutex_unlock(&data->mutex);
    return ret;
}

static long my_ioctl(struct file *file, unsigned int cmd, unsigned long arg)
{
    struct my_device_data *data = file->private_data;

    switch (cmd) {
    case MY_IOCTL_GET_STATUS:
        return put_user(data->status, (int __user *)arg);

    case MY_IOCTL_SET_CONFIG:
        if (!capable(CAP_SYS_ADMIN))
            return -EPERM;
        return configure_device(data, arg);

    default:
        return -ENOTTY;
    }
}

static const struct file_operations my_fops = {
    .owner = THIS_MODULE,
    .open = my_open,
    .release = my_release,
    .read = my_read,
    .write = my_write,
    .unlocked_ioctl = my_ioctl,
    .llseek = no_llseek,
};</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Best Practices Summary -->
            <section class="content-section">
                <div class="section-header">
                    <h2>✅ Best Practices Summary</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="best-practices-grid">
                        <div class="practice-item">
                            <h4>🔒 Locking</h4>
                            <ul>
                                <li>Use the least restrictive lock possible</li>
                                <li>Hold locks for the shortest time</li>
                                <li>Avoid nested locking when possible</li>
                                <li>Use lockdep annotations</li>
                                <li>Consider RCU for read-mostly data</li>
                            </ul>
                        </div>

                        <div class="practice-item">
                            <h4>💾 Memory</h4>
                            <ul>
                                <li>Always check allocation return values</li>
                                <li>Use appropriate GFP flags</li>
                                <li>Free resources in reverse order</li>
                                <li>Use devm_* functions when possible</li>
                                <li>Avoid memory leaks with proper cleanup</li>
                            </ul>
                        </div>

                        <div class="practice-item">
                            <h4>⚠️ Error Handling</h4>
                            <ul>
                                <li>Use standard errno values</li>
                                <li>Provide informative error messages</li>
                                <li>Clean up on all error paths</li>
                                <li>Use goto for cleanup sequences</li>
                                <li>Handle interrupted system calls</li>
                            </ul>
                        </div>

                        <div class="practice-item">
                            <h4>🔧 Code Style</h4>
                            <ul>
                                <li>Follow kernel coding style</li>
                                <li>Use meaningful variable names</li>
                                <li>Keep functions short and focused</li>
                                <li>Add proper documentation</li>
                                <li>Use static for internal functions</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/search.js"></script>
    <script src="../assets/js/syntax.js"></script>
</body>
</html>
