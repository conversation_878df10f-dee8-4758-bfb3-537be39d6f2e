/**
 * Advanced Search Implementation for Linux Kernel Documentation
 * Provides full-text search with indexing and ranking
 */

class KernelSearch {
  constructor() {
    this.index = new Map();
    this.documents = new Map();
    this.stopWords = new Set([
      'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
      'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
      'to', 'was', 'will', 'with', 'the', 'this', 'but', 'they', 'have',
      'had', 'what', 'said', 'each', 'which', 'their', 'time', 'if'
    ]);
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      // Load search index if available
      const response = await fetch('/docs/search-index.json');
      if (response.ok) {
        const data = await response.json();
        this.loadIndex(data);
      } else {
        // Build index from current page
        this.buildIndexFromPage();
      }
      this.initialized = true;
    } catch (error) {
      console.warn('Failed to load search index, building from current page:', error);
      this.buildIndexFromPage();
      this.initialized = true;
    }
  }

  loadIndex(data) {
    this.documents = new Map(data.documents);
    this.index = new Map();
    
    // Rebuild index from documents
    for (const [docId, doc] of this.documents) {
      this.indexDocument(docId, doc);
    }
  }

  buildIndexFromPage() {
    const sections = document.querySelectorAll('section, .content-section');
    
    sections.forEach((section, index) => {
      const docId = `page-${index}`;
      const title = section.querySelector('h1, h2, h3, h4, h5, h6')?.textContent || 'Untitled';
      const content = this.extractTextContent(section);
      const url = this.getElementUrl(section);
      
      const doc = {
        id: docId,
        title: title,
        content: content,
        url: url,
        type: this.getContentType(section)
      };
      
      this.documents.set(docId, doc);
      this.indexDocument(docId, doc);
    });
  }

  indexDocument(docId, doc) {
    const text = `${doc.title} ${doc.content}`.toLowerCase();
    const words = this.tokenize(text);
    
    words.forEach(word => {
      if (!this.stopWords.has(word) && word.length > 2) {
        if (!this.index.has(word)) {
          this.index.set(word, new Map());
        }
        
        const termIndex = this.index.get(word);
        if (!termIndex.has(docId)) {
          termIndex.set(docId, 0);
        }
        termIndex.set(docId, termIndex.get(docId) + 1);
      }
    });
  }

  tokenize(text) {
    return text
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  search(query, maxResults = 10) {
    if (!this.initialized) {
      console.warn('Search not initialized');
      return [];
    }

    const terms = this.tokenize(query.toLowerCase())
      .filter(term => !this.stopWords.has(term) && term.length > 2);
    
    if (terms.length === 0) return [];

    const scores = new Map();
    const termCounts = new Map();

    // Calculate TF-IDF scores
    terms.forEach(term => {
      const termIndex = this.index.get(term);
      if (!termIndex) return;

      const idf = Math.log(this.documents.size / termIndex.size);
      
      termIndex.forEach((tf, docId) => {
        const doc = this.documents.get(docId);
        if (!doc) return;

        const score = tf * idf;
        
        if (!scores.has(docId)) {
          scores.set(docId, 0);
          termCounts.set(docId, 0);
        }
        
        scores.set(docId, scores.get(docId) + score);
        termCounts.set(docId, termCounts.get(docId) + 1);
      });
    });

    // Boost documents that contain more query terms
    scores.forEach((score, docId) => {
      const termRatio = termCounts.get(docId) / terms.length;
      scores.set(docId, score * (1 + termRatio));
    });

    // Sort by score and return top results
    const results = Array.from(scores.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxResults)
      .map(([docId, score]) => {
        const doc = this.documents.get(docId);
        return {
          ...doc,
          score: score,
          snippet: this.generateSnippet(doc.content, terms)
        };
      });

    return results;
  }

  generateSnippet(content, terms, maxLength = 150) {
    const words = content.split(' ');
    let bestStart = 0;
    let bestScore = 0;
    
    // Find the best window of words that contains the most search terms
    for (let i = 0; i < words.length - 20; i++) {
      const window = words.slice(i, i + 20).join(' ').toLowerCase();
      const score = terms.reduce((acc, term) => {
        return acc + (window.includes(term) ? 1 : 0);
      }, 0);
      
      if (score > bestScore) {
        bestScore = score;
        bestStart = i;
      }
    }
    
    let snippet = words.slice(bestStart, bestStart + 20).join(' ');
    
    if (snippet.length > maxLength) {
      snippet = snippet.substring(0, maxLength) + '...';
    }
    
    // Highlight search terms
    terms.forEach(term => {
      const regex = new RegExp(`\\b${this.escapeRegex(term)}\\b`, 'gi');
      snippet = snippet.replace(regex, `<mark>$&</mark>`);
    });
    
    return snippet;
  }

  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  extractTextContent(element) {
    // Clone the element to avoid modifying the original
    const clone = element.cloneNode(true);
    
    // Remove script and style elements
    clone.querySelectorAll('script, style, .code-copy').forEach(el => el.remove());
    
    return clone.textContent || clone.innerText || '';
  }

  getElementUrl(element) {
    const id = element.id || element.querySelector('[id]')?.id;
    if (id) {
      return `${window.location.pathname}#${id}`;
    }
    return window.location.pathname;
  }

  getContentType(element) {
    if (element.classList.contains('api-section')) return 'API Reference';
    if (element.classList.contains('config-section')) return 'Configuration';
    if (element.querySelector('pre code')) return 'Code Example';
    if (element.querySelector('h1')) return 'Main Section';
    if (element.querySelector('h2')) return 'Subsection';
    return 'Content';
  }

  // Advanced search features
  searchWithFilters(query, filters = {}) {
    let results = this.search(query, 50);
    
    // Apply type filter
    if (filters.type) {
      results = results.filter(result => result.type === filters.type);
    }
    
    // Apply subsystem filter
    if (filters.subsystem) {
      results = results.filter(result => 
        result.url.includes(`/${filters.subsystem}/`) ||
        result.title.toLowerCase().includes(filters.subsystem.toLowerCase())
      );
    }
    
    // Apply minimum score threshold
    if (filters.minScore) {
      results = results.filter(result => result.score >= filters.minScore);
    }
    
    return results.slice(0, filters.maxResults || 10);
  }

  // Autocomplete suggestions
  getSuggestions(query, maxSuggestions = 5) {
    const terms = this.tokenize(query.toLowerCase());
    const lastTerm = terms[terms.length - 1];
    
    if (!lastTerm || lastTerm.length < 2) return [];
    
    const suggestions = [];
    
    for (const [word] of this.index) {
      if (word.startsWith(lastTerm) && word !== lastTerm) {
        suggestions.push(word);
      }
    }
    
    return suggestions
      .sort()
      .slice(0, maxSuggestions)
      .map(word => {
        const newTerms = [...terms.slice(0, -1), word];
        return newTerms.join(' ');
      });
  }

  // Export search index for caching
  exportIndex() {
    return {
      documents: Array.from(this.documents.entries()),
      timestamp: Date.now()
    };
  }

  // Search analytics
  getSearchStats() {
    return {
      totalDocuments: this.documents.size,
      totalTerms: this.index.size,
      averageDocumentLength: this.getAverageDocumentLength(),
      mostCommonTerms: this.getMostCommonTerms(10)
    };
  }

  getAverageDocumentLength() {
    let totalLength = 0;
    for (const doc of this.documents.values()) {
      totalLength += doc.content.length;
    }
    return Math.round(totalLength / this.documents.size);
  }

  getMostCommonTerms(count = 10) {
    const termCounts = new Map();
    
    for (const [term, docs] of this.index) {
      let totalCount = 0;
      for (const count of docs.values()) {
        totalCount += count;
      }
      termCounts.set(term, totalCount);
    }
    
    return Array.from(termCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([term, count]) => ({ term, count }));
  }
}

// Global search instance
window.kernelSearch = new KernelSearch();

// Initialize search when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.kernelSearch.initialize();
});
