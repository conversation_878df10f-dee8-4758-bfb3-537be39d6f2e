/* Syntax Highlighting for C Code */

/* Base syntax highlighting */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 1rem;
  background: var(--code-bg);
  color: var(--text-primary);
}

/* Light theme syntax colors */
:root {
  --syntax-comment: #6a737d;
  --syntax-keyword: #d73a49;
  --syntax-string: #032f62;
  --syntax-number: #005cc5;
  --syntax-function: #6f42c1;
  --syntax-type: #d73a49;
  --syntax-variable: #e36209;
  --syntax-preprocessor: #735c0f;
  --syntax-operator: #d73a49;
}

/* Medium theme syntax colors */
[data-theme="medium"] {
  --syntax-comment: #5a5a5a;
  --syntax-keyword: #c7254e;
  --syntax-string: #2c5282;
  --syntax-number: #0056b3;
  --syntax-function: #553c9a;
  --syntax-type: #c7254e;
  --syntax-variable: #d69e2e;
  --syntax-preprocessor: #6b5b00;
  --syntax-operator: #c7254e;
}

/* Dark theme syntax colors */
[data-theme="dark"] {
  --syntax-comment: #8b949e;
  --syntax-keyword: #ff7b72;
  --syntax-string: #a5d6ff;
  --syntax-number: #79c0ff;
  --syntax-function: #d2a8ff;
  --syntax-type: #ff7b72;
  --syntax-variable: #ffa657;
  --syntax-preprocessor: #f0d000;
  --syntax-operator: #ff7b72;
}

/* Syntax highlighting classes */
.hljs-comment,
.hljs-quote {
  color: var(--syntax-comment);
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: var(--syntax-keyword);
  font-weight: bold;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: var(--syntax-number);
}

.hljs-string,
.hljs-doctag {
  color: var(--syntax-string);
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: var(--syntax-function);
  font-weight: bold;
}

.hljs-type,
.hljs-class .hljs-title {
  color: var(--syntax-type);
  font-weight: bold;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
  color: var(--syntax-variable);
  font-weight: normal;
}

.hljs-regexp,
.hljs-link {
  color: var(--syntax-string);
}

.hljs-symbol,
.hljs-bullet {
  color: var(--syntax-number);
}

.hljs-built_in,
.hljs-builtin-name {
  color: var(--syntax-function);
}

.hljs-meta {
  color: var(--syntax-preprocessor);
  font-weight: bold;
}

.hljs-meta-string {
  color: var(--syntax-string);
}

.hljs-deletion {
  background: #ffeef0;
}

.hljs-addition {
  background: #f0fff4;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

/* C-specific highlighting */
.hljs-c .hljs-keyword {
  color: var(--syntax-keyword);
}

.hljs-c .hljs-built_in {
  color: var(--syntax-type);
}

.hljs-c .hljs-function {
  color: var(--syntax-function);
}

.hljs-c .hljs-params {
  color: var(--text-primary);
}

.hljs-c .hljs-title {
  color: var(--syntax-function);
}

/* Kernel-specific highlighting */
.kernel-macro {
  color: var(--syntax-preprocessor);
  font-weight: bold;
}

.kernel-type {
  color: var(--syntax-type);
  font-weight: bold;
}

.kernel-function {
  color: var(--syntax-function);
}

.kernel-constant {
  color: var(--syntax-number);
  font-weight: bold;
}

.kernel-struct {
  color: var(--syntax-type);
  font-weight: bold;
}

.kernel-enum {
  color: var(--syntax-type);
  font-weight: bold;
}

/* Line numbers */
.hljs-ln-numbers {
  text-align: right;
  color: var(--text-muted);
  border-right: 1px solid var(--border-color);
  vertical-align: top;
  padding-right: 5px;
  user-select: none;
}

.hljs-ln-code {
  padding-left: 10px;
}

/* Code block enhancements */
.code-block {
  position: relative;
  margin: 1rem 0;
}

.code-header {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-bottom: none;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
  border-radius: 4px 4px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code-language {
  font-weight: 600;
}

.code-copy {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.code-copy:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.code-block pre {
  margin: 0;
  border-radius: 0 0 4px 4px;
}

.code-block.has-header pre {
  border-top: none;
  border-radius: 0 0 4px 4px;
}

/* Inline code in different contexts */
.api-signature code {
  background: var(--bg-tertiary);
  color: var(--syntax-function);
  font-weight: 600;
}

.parameter-type code {
  background: var(--bg-tertiary);
  color: var(--syntax-type);
  font-weight: 600;
}

.return-type code {
  background: var(--bg-tertiary);
  color: var(--syntax-type);
  font-weight: 600;
}

/* Diff highlighting */
.hljs-addition {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success);
}

.hljs-deletion {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger);
}

/* Special kernel constructs */
.syscall {
  color: var(--syntax-function);
  font-weight: bold;
}

.interrupt-handler {
  color: var(--syntax-function);
  font-weight: bold;
}

.module-param {
  color: var(--syntax-variable);
  font-weight: bold;
}

.config-option {
  color: var(--syntax-preprocessor);
  font-weight: bold;
}

/* Error and warning highlighting */
.syntax-error {
  background-color: rgba(220, 53, 69, 0.1);
  border-bottom: 2px wavy var(--danger);
}

.syntax-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-bottom: 2px wavy var(--warning);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hljs {
    font-size: 0.8rem;
    padding: 0.75rem;
  }
  
  .code-header {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .hljs-ln-numbers {
    display: none;
  }
}

/* Print styles for code */
@media print {
  .hljs {
    background: white !important;
    color: black !important;
  }
  
  .hljs-comment,
  .hljs-quote {
    color: #666 !important;
  }
  
  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-subst {
    color: #000 !important;
    font-weight: bold;
  }
  
  .code-copy {
    display: none;
  }
}
