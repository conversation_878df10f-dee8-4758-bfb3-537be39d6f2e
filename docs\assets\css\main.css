/* Linux Kernel Documentation - Main Stylesheet */

/* CSS Custom Properties for Theming */
:root {
  /* Light Theme (default) */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #868e96;
  --accent-primary: #0066cc;
  --accent-secondary: #004499;
  --border-color: #dee2e6;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --code-bg: #f8f9fa;
  --code-border: #e9ecef;
  --nav-bg: #ffffff;
  --nav-border: #dee2e6;
  --success: #28a745;
  --warning: #ffc107;
  --danger: #dc3545;
  --info: #17a2b8;
}

/* Medium Theme */
[data-theme="medium"] {
  --bg-primary: #f5f5f5;
  --bg-secondary: #e8e8e8;
  --bg-tertiary: #d4d4d4;
  --text-primary: #2c2c2c;
  --text-secondary: #5a5a5a;
  --text-muted: #777777;
  --accent-primary: #0056b3;
  --accent-secondary: #003d82;
  --border-color: #c6c6c6;
  --shadow-light: rgba(0, 0, 0, 0.15);
  --shadow-medium: rgba(0, 0, 0, 0.2);
  --code-bg: #e8e8e8;
  --code-border: #d4d4d4;
  --nav-bg: #f5f5f5;
  --nav-border: #c6c6c6;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #404040;
  --text-primary: #e9ecef;
  --text-secondary: #adb5bd;
  --text-muted: #6c757d;
  --accent-primary: #4dabf7;
  --accent-secondary: #339af0;
  --border-color: #495057;
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-medium: rgba(0, 0, 0, 0.4);
  --code-bg: #2d2d2d;
  --code-border: #495057;
  --nav-bg: #1a1a1a;
  --nav-border: #495057;
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.1rem; }

p {
  margin-bottom: 1rem;
  color: var(--text-primary);
}

a {
  color: var(--accent-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--accent-secondary);
  text-decoration: underline;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.main-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  grid-template-rows: auto 1fr auto;
  min-height: 100vh;
  gap: 0;
}

/* Header */
.header {
  grid-column: 1 / -1;
  background-color: var(--nav-bg);
  border-bottom: 1px solid var(--nav-border);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px var(--shadow-light);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  text-decoration: none;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Theme Selector */
.theme-selector {
  position: relative;
}

.theme-button {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.theme-button:hover {
  background: var(--bg-tertiary);
}

.theme-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  box-shadow: 0 4px 12px var(--shadow-medium);
  min-width: 120px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
}

.theme-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.theme-option {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  color: var(--text-primary);
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.theme-option:hover {
  background: var(--bg-secondary);
}

/* Search */
.search-container {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 4px 4px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
}

.search-result {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-result:hover {
  background: var(--bg-secondary);
}

.search-result:last-child {
  border-bottom: none;
}

/* Sidebar Navigation */
.sidebar {
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  padding: 1rem;
  overflow-y: auto;
  position: sticky;
  top: 80px;
  height: calc(100vh - 80px);
}

.nav-tree {
  list-style: none;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: block;
  padding: 0.5rem 0.75rem;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.nav-link:hover {
  background: var(--bg-tertiary);
  text-decoration: none;
}

.nav-link.active {
  background: var(--accent-primary);
  color: white;
}

.nav-submenu {
  list-style: none;
  margin-left: 1rem;
  margin-top: 0.25rem;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.nav-submenu.expanded {
  max-height: 500px;
}

.nav-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  margin-right: 0.5rem;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.nav-toggle:hover {
  background: var(--bg-tertiary);
}

.nav-toggle.expanded {
  transform: rotate(90deg);
}

/* Main Content */
.main-content {
  padding: 2rem;
  max-width: none;
}

.breadcrumb {
  margin-bottom: 2rem;
  padding: 0.75rem 1rem;
  background: var(--bg-secondary);
  border-radius: 4px;
  font-size: 0.9rem;
}

.breadcrumb a {
  color: var(--accent-primary);
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: var(--text-muted);
}

/* Content Sections */
.content-section {
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-color);
}

.section-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.section-toggle:hover {
  background: var(--bg-secondary);
}

.section-content {
  transition: all 0.3s ease;
}

.section-content.collapsed {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
}

/* Code Blocks */
pre {
  background: var(--code-bg);
  border: 1px solid var(--code-border);
  border-radius: 4px;
  padding: 1rem;
  overflow-x: auto;
  margin: 1rem 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
}

code {
  background: var(--code-bg);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

pre code {
  background: none;
  padding: 0;
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  background: var(--bg-primary);
}

th, td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
}

tr:hover {
  background: var(--bg-secondary);
}

/* Cards */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px var(--shadow-light);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 8px var(--shadow-medium);
}

.card-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.card-title {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.card-subtitle {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-layout {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    display: none;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .search-container {
    width: 100%;
    max-width: 300px;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .sidebar,
  .header-controls,
  .theme-selector,
  .search-container {
    display: none;
  }
  
  .main-layout {
    grid-template-columns: 1fr;
  }
  
  .main-content {
    padding: 0;
  }
  
  a {
    color: inherit;
    text-decoration: underline;
  }
}
