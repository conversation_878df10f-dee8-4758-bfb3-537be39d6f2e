<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive documentation for Linux kernel memory management subsystem (mm/)">
    <meta name="keywords" content="Linux kernel, memory management, virtual memory, page allocation, slab allocator">
    
    <title>Memory Management (mm/) - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="../../index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="../../index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle expanded" aria-expanded="true">▼</button>
                    <a href="../index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu expanded">
                        <li class="nav-item">
                            <a href="index.html" class="nav-link active">💾 Memory Management (mm/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../kernel/index.html" class="nav-link">⚙️ Core Kernel (kernel/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../fs/index.html" class="nav-link">📁 File Systems (fs/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../net/index.html" class="nav-link">🌐 Networking (net/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../drivers/index.html" class="nav-link">🔌 Device Drivers (drivers/)</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle" aria-expanded="false">▶</button>
                    <a href="../../api/index.html" class="nav-link">📚 API Reference</a>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="../../api/memory.html" class="nav-link">Memory APIs</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="../../patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="../../index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="../index.html">Core Subsystems</a>
                <span class="breadcrumb-separator">›</span>
                <span>Memory Management (mm/)</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>💾 Memory Management Subsystem (mm/)</h1>
                </div>
                <div class="section-content">
                    <p>
                        The memory management subsystem is one of the most critical components of the Linux kernel, 
                        responsible for managing system memory efficiently and providing virtual memory services 
                        to both kernel and user space. It implements sophisticated algorithms for memory allocation, 
                        reclaim, and optimization across diverse hardware architectures.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🎯 Key Responsibilities</h3>
                        </div>
                        <ul>
                            <li><strong>Virtual Memory Management:</strong> Mapping virtual addresses to physical memory</li>
                            <li><strong>Page Allocation:</strong> Managing physical memory pages through the buddy allocator</li>
                            <li><strong>Slab Allocation:</strong> Efficient allocation of kernel objects</li>
                            <li><strong>Memory Reclaim:</strong> Freeing unused memory when system is under pressure</li>
                            <li><strong>Swap Management:</strong> Moving pages to/from secondary storage</li>
                            <li><strong>NUMA Support:</strong> Optimizing memory access on multi-node systems</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Architecture Overview -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🏗️ Memory Management Architecture</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="architecture-diagram">
                        <pre class="architecture-ascii">
┌─────────────────────────────────────────────────────────────┐
│                    User Space Applications                   │
├─────────────────────────────────────────────────────────────┤
│                     System Call Interface                   │
│                  (mmap, brk, malloc, etc.)                  │
├─────────────────────────────────────────────────────────────┤
│                Virtual Memory Management                     │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │     VMA     │   Page      │   Memory    │    Swap     │  │
│  │  Management │   Tables    │   Mapping   │  Management │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                   Physical Memory Management                 │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │    Buddy    │    Slab     │    Page     │   Memory    │  │
│  │  Allocator  │  Allocator  │   Reclaim   │  Compaction │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                      Physical Memory                        │
│                    (RAM, NUMA Nodes)                       │
└─────────────────────────────────────────────────────────────┘
                        </pre>
                    </div>
                    
                    <h3>🔧 Core Components</h3>
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Buddy Allocator</h4>
                                <p class="card-subtitle">Physical page allocation</p>
                            </div>
                            <p>
                                The buddy allocator manages physical memory pages using a binary tree structure. 
                                It efficiently handles allocation and deallocation of contiguous memory blocks 
                                while minimizing fragmentation.
                            </p>
                            <p><strong>Files:</strong> <code>page_alloc.c</code>, <code>internal.h</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Slab/SLUB Allocator</h4>
                                <p class="card-subtitle">Kernel object allocation</p>
                            </div>
                            <p>
                                Provides efficient allocation of frequently used kernel objects. SLUB is the 
                                default allocator that reduces memory overhead and improves cache performance.
                            </p>
                            <p><strong>Files:</strong> <code>slub.c</code>, <code>slab_common.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Virtual Memory Areas</h4>
                                <p class="card-subtitle">Process memory mapping</p>
                            </div>
                            <p>
                                VMAs represent contiguous virtual memory regions in a process address space. 
                                They track permissions, backing store, and other attributes for memory regions.
                            </p>
                            <p><strong>Files:</strong> <code>mmap.c</code>, <code>vma.c</code>, <code>vma_init.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Page Reclaim</h4>
                                <p class="card-subtitle">Memory pressure handling</p>
                            </div>
                            <p>
                                Implements algorithms to free memory when the system is under pressure. 
                                Uses LRU lists and other heuristics to select pages for eviction.
                            </p>
                            <p><strong>Files:</strong> <code>vmscan.c</code>, <code>workingset.c</code></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Key Data Structures -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📊 Key Data Structures</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>struct page</h3>
                        <div class="api-signature">
                            <code>struct page</code> - Represents a physical memory page
                        </div>
                        <p>
                            The fundamental data structure representing a physical page of memory. 
                            Contains metadata about the page's state, usage, and relationships.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct page {
    unsigned long flags;          /* Page flags (PG_locked, PG_dirty, etc.) */
    union {
        struct {
            struct list_head lru;  /* LRU list linkage */
            struct address_space *mapping; /* File mapping */
            pgoff_t index;         /* Offset within mapping */
        };
        struct {
            void *freelist;        /* SLUB: freelist pointer */
            union {
                struct kmem_cache *slab_cache;
                struct page *first_page;
            };
        };
    };
    atomic_t _refcount;           /* Reference count */
    atomic_t _mapcount;           /* Page table mappings */
};</code></pre>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>struct vm_area_struct</h3>
                        <div class="api-signature">
                            <code>struct vm_area_struct</code> - Virtual memory area descriptor
                        </div>
                        <p>
                            Describes a contiguous virtual memory region within a process address space.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct vm_area_struct {
    unsigned long vm_start;       /* Start address */
    unsigned long vm_end;         /* End address */
    struct vm_area_struct *vm_next; /* Next VMA in list */
    pgprot_t vm_page_prot;        /* Access permissions */
    unsigned long vm_flags;       /* VMA flags (VM_READ, VM_WRITE, etc.) */
    struct mm_struct *vm_mm;      /* Associated mm_struct */
    struct file *vm_file;         /* Backing file (if any) */
    const struct vm_operations_struct *vm_ops; /* VMA operations */
};</code></pre>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>struct mm_struct</h3>
                        <div class="api-signature">
                            <code>struct mm_struct</code> - Process memory descriptor
                        </div>
                        <p>
                            Contains all memory management information for a process, including VMAs, 
                            page tables, and memory statistics.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct mm_struct {
    struct vm_area_struct *mmap;  /* List of VMAs */
    struct rb_root mm_rb;         /* Red-black tree of VMAs */
    pgd_t *pgd;                   /* Page global directory */
    atomic_t mm_users;            /* Number of users */
    atomic_t mm_count;            /* Reference count */
    unsigned long start_code;     /* Code segment start */
    unsigned long end_code;       /* Code segment end */
    unsigned long start_data;     /* Data segment start */
    unsigned long end_data;       /* Data segment end */
    unsigned long start_brk;      /* Heap start */
    unsigned long brk;            /* Current heap end */
    unsigned long start_stack;    /* Stack start */
};</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Memory Allocation APIs -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔧 Memory Allocation APIs</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>Page Allocation Functions</h3>
                        
                        <div class="api-function">
                            <div class="api-signature">
                                <code>struct page *alloc_pages(gfp_t gfp_mask, unsigned int order)</code>
                            </div>
                            <p><strong>Purpose:</strong> Allocate 2^order contiguous pages</p>
                            <p><strong>Parameters:</strong></p>
                            <ul>
                                <li><code class="parameter-type">gfp_t</code> <strong>gfp_mask</strong> - Allocation flags (GFP_KERNEL, GFP_ATOMIC, etc.)</li>
                                <li><code class="parameter-type">unsigned int</code> <strong>order</strong> - Log2 of number of pages to allocate</li>
                            </ul>
                            <p><strong>Returns:</strong> <code class="return-type">struct page *</code> - Pointer to first page or NULL on failure</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* Allocate a single page */
struct page *page = alloc_pages(GFP_KERNEL, 0);
if (!page) {
    pr_err("Failed to allocate page\n");
    return -ENOMEM;
}

/* Allocate 4 contiguous pages (16KB on x86) */
struct page *pages = alloc_pages(GFP_KERNEL, 2);
if (pages) {
    void *addr = page_address(pages);
    /* Use the allocated memory */
    __free_pages(pages, 2);
}</code></pre>
                            </div>
                        </div>

                        <div class="api-function">
                            <div class="api-signature">
                                <code>void *kmalloc(size_t size, gfp_t flags)</code>
                            </div>
                            <p><strong>Purpose:</strong> Allocate kernel memory of specified size</p>
                            <p><strong>Parameters:</strong></p>
                            <ul>
                                <li><code class="parameter-type">size_t</code> <strong>size</strong> - Number of bytes to allocate</li>
                                <li><code class="parameter-type">gfp_t</code> <strong>flags</strong> - Allocation flags</li>
                            </ul>
                            <p><strong>Returns:</strong> <code class="return-type">void *</code> - Pointer to allocated memory or NULL on failure</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* Allocate memory for a data structure */
struct my_struct *ptr = kmalloc(sizeof(struct my_struct), GFP_KERNEL);
if (!ptr)
    return -ENOMEM;

/* Initialize and use the structure */
memset(ptr, 0, sizeof(struct my_struct));
/* ... use ptr ... */

/* Free the memory */
kfree(ptr);</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Memory Management Patterns -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🎯 Common Memory Management Patterns</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="pattern-section">
                        <h3>Pattern 1: Safe Memory Allocation</h3>
                        <p>Always check return values and handle allocation failures gracefully.</p>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* Good: Check for allocation failure */
void *buffer = kmalloc(size, GFP_KERNEL);
if (!buffer) {
    pr_err("Memory allocation failed for %zu bytes\n", size);
    return -ENOMEM;
}

/* Use buffer... */

/* Always free allocated memory */
kfree(buffer);
buffer = NULL; /* Prevent double-free */</code></pre>
                        </div>
                    </div>

                    <div class="pattern-section">
                        <h3>Pattern 2: Reference Counting</h3>
                        <p>Use reference counting to manage shared memory objects safely.</p>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* Get a reference to a page */
struct page *page = alloc_pages(GFP_KERNEL, 0);
if (page) {
    get_page(page);  /* Increment reference count */

    /* Share the page with another subsystem */
    share_page_with_subsystem(page);

    /* Release our reference */
    put_page(page);  /* Decrements refcount, frees if zero */
}</code></pre>
                        </div>
                    </div>

                    <div class="pattern-section">
                        <h3>Pattern 3: GFP Flags Selection</h3>
                        <p>Choose appropriate GFP flags based on context and requirements.</p>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* In process context - can sleep */
void *ptr1 = kmalloc(size, GFP_KERNEL);

/* In interrupt context - cannot sleep */
void *ptr2 = kmalloc(size, GFP_ATOMIC);

/* For user-accessible memory */
void *ptr3 = kmalloc(size, GFP_USER);

/* For DMA-capable memory */
void *ptr4 = kmalloc(size, GFP_DMA);

/* Zero-filled memory */
void *ptr5 = kzalloc(size, GFP_KERNEL);</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Configuration Options -->
            <section class="content-section">
                <div class="section-header">
                    <h2>⚙️ Configuration Options</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="config-grid">
                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_SLUB</code></h4>
                            <p>Enable SLUB allocator (default). More efficient than SLAB with lower memory overhead.</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_SLUB_DEBUG</code></h4>
                            <p>Enable SLUB debugging features including poisoning, red-zoning, and tracking.</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_NUMA</code></h4>
                            <p>Enable Non-Uniform Memory Access support for multi-node systems.</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_HIGHMEM</code></h4>
                            <p>Support for high memory on 32-bit systems (>896MB on x86).</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_TRANSPARENT_HUGEPAGE</code></h4>
                            <p>Enable transparent huge pages for improved performance.</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_MEMORY_HOTPLUG</code></h4>
                            <p>Support for adding/removing memory at runtime.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- File Organization -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📁 File Organization</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="file-tree">
                        <h3>mm/ Directory Structure</h3>
                        <pre class="file-listing">
mm/
├── <strong>page_alloc.c</strong>        # Buddy allocator implementation
├── <strong>slub.c</strong>              # SLUB allocator
├── <strong>slab_common.c</strong>       # Common slab allocator code
├── <strong>vmscan.c</strong>            # Page reclaim and LRU management
├── <strong>mmap.c</strong>              # Memory mapping operations
├── <strong>memory.c</strong>            # Page fault handling
├── <strong>swap.c</strong>              # Swap cache management
├── <strong>swapfile.c</strong>          # Swap file operations
├── <strong>vmalloc.c</strong>           # Virtual memory allocation
├── <strong>percpu.c</strong>            # Per-CPU memory allocation
├── <strong>memcontrol.c</strong>        # Memory control groups
├── <strong>oom_kill.c</strong>          # Out-of-memory killer
├── <strong>compaction.c</strong>        # Memory compaction
├── <strong>migrate.c</strong>           # Page migration
├── <strong>hugetlb.c</strong>           # Huge page support
├── <strong>mprotect.c</strong>          # Memory protection
├── <strong>mremap.c</strong>            # Memory remapping
├── <strong>msync.c</strong>             # Memory synchronization
├── <strong>madvise.c</strong>           # Memory advice
├── <strong>mincore.c</strong>           # Memory residency information
├── <strong>mlock.c</strong>             # Memory locking
├── <strong>nommu.c</strong>             # No-MMU support
├── <strong>highmem.c</strong>           # High memory support
├── <strong>memory-failure.c</strong>    # Memory error handling
├── <strong>page_owner.c</strong>        # Page allocation tracking
├── <strong>debug.c</strong>             # Memory debugging
├── <strong>init-mm.c</strong>           # Initial mm_struct
├── <strong>maccess.c</strong>           # Safe memory access
├── <strong>gup.c</strong>               # Get user pages
├── <strong>internal.h</strong>          # Internal definitions
└── <strong>Makefile</strong>            # Build configuration
                        </pre>
                    </div>
                </div>
            </section>

            <!-- Performance Considerations -->
            <section class="content-section">
                <div class="section-header">
                    <h2>⚡ Performance Considerations</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="performance-tips">
                        <div class="tip-item">
                            <h4>🎯 Choose the Right Allocator</h4>
                            <ul>
                                <li>Use <code>kmalloc()</code> for small, temporary allocations</li>
                                <li>Use <code>vmalloc()</code> for large allocations that don't need to be contiguous</li>
                                <li>Use <code>alloc_pages()</code> when you need specific page properties</li>
                                <li>Use per-CPU allocators for frequently accessed data</li>
                            </ul>
                        </div>

                        <div class="tip-item">
                            <h4>🔄 Memory Locality</h4>
                            <ul>
                                <li>Allocate memory on the same NUMA node where it will be used</li>
                                <li>Use <code>kmalloc_node()</code> for NUMA-aware allocations</li>
                                <li>Consider cache line alignment for frequently accessed structures</li>
                            </ul>
                        </div>

                        <div class="tip-item">
                            <h4>📊 Memory Pressure</h4>
                            <ul>
                                <li>Use <code>GFP_NOWARN</code> for allocations that can fail gracefully</li>
                                <li>Implement proper error handling for allocation failures</li>
                                <li>Consider using memory pools for critical allocations</li>
                                <li>Monitor memory usage with <code>/proc/meminfo</code> and <code>/proc/slabinfo</code></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Related Documentation -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📚 Related Documentation</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="related-links">
                        <div class="link-category">
                            <h4>API References</h4>
                            <ul>
                                <li><a href="../../api/memory.html">Memory Management APIs</a></li>
                                <li><a href="../../api/page-allocation.html">Page Allocation APIs</a></li>
                                <li><a href="../../api/slab.html">Slab Allocator APIs</a></li>
                            </ul>
                        </div>

                        <div class="link-category">
                            <h4>Architecture-Specific</h4>
                            <ul>
                                <li><a href="../../arch/x86/memory.html">x86 Memory Management</a></li>
                                <li><a href="../../arch/arm64/memory.html">ARM64 Memory Management</a></li>
                                <li><a href="../../arch/riscv/memory.html">RISC-V Memory Management</a></li>
                            </ul>
                        </div>

                        <div class="link-category">
                            <h4>Debugging and Tools</h4>
                            <ul>
                                <li><a href="../../tools/memory-debugging.html">Memory Debugging Tools</a></li>
                                <li><a href="../../tools/kmemleak.html">Kernel Memory Leak Detector</a></li>
                                <li><a href="../../tools/slub-debug.html">SLUB Debugging</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/search.js"></script>
    <script src="../../assets/js/syntax.js"></script>
</body>
</html>
