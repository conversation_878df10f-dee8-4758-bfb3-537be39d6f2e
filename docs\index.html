<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive documentation for the Linux kernel codebase, including all subsystems, APIs, and coding patterns.">
    <meta name="keywords" content="Linux kernel, documentation, memory management, file systems, networking, device drivers, architecture">
    <meta name="author" content="Linux Kernel Documentation Project">
    
    <title>Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Open Graph meta tags -->
    <meta property="og:title" content="Linux Kernel Documentation">
    <meta property="og:description" content="Comprehensive documentation for the Linux kernel codebase">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://kernel-docs.example.com">
    
    <!-- Structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "TechArticle",
        "headline": "Linux Kernel Documentation",
        "description": "Comprehensive documentation for the Linux kernel codebase",
        "author": {
            "@type": "Organization",
            "name": "Linux Kernel Documentation Project"
        },
        "datePublished": "2024-01-01",
        "dateModified": "2024-01-01"
    }
    </script>
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="index.html" class="nav-link active">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle" aria-expanded="false">▶</button>
                    <a href="subsystems/index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="subsystems/mm/index.html" class="nav-link">💾 Memory Management (mm/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="subsystems/kernel/index.html" class="nav-link">⚙️ Core Kernel (kernel/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="subsystems/fs/index.html" class="nav-link">📁 File Systems (fs/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="subsystems/net/index.html" class="nav-link">🌐 Networking (net/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="subsystems/drivers/index.html" class="nav-link">🔌 Device Drivers (drivers/)</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle" aria-expanded="false">▶</button>
                    <a href="arch/index.html" class="nav-link">🏗️ Architecture Support</a>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="arch/x86/index.html" class="nav-link">x86/x86_64</a>
                        </li>
                        <li class="nav-item">
                            <a href="arch/arm/index.html" class="nav-link">ARM</a>
                        </li>
                        <li class="nav-item">
                            <a href="arch/arm64/index.html" class="nav-link">ARM64</a>
                        </li>
                        <li class="nav-item">
                            <a href="arch/powerpc/index.html" class="nav-link">PowerPC</a>
                        </li>
                        <li class="nav-item">
                            <a href="arch/riscv/index.html" class="nav-link">RISC-V</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle" aria-expanded="false">▶</button>
                    <a href="api/index.html" class="nav-link">📚 API Reference</a>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="api/memory.html" class="nav-link">Memory APIs</a>
                        </li>
                        <li class="nav-item">
                            <a href="api/filesystem.html" class="nav-link">Filesystem APIs</a>
                        </li>
                        <li class="nav-item">
                            <a href="api/networking.html" class="nav-link">Networking APIs</a>
                        </li>
                        <li class="nav-item">
                            <a href="api/driver.html" class="nav-link">Driver APIs</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
                
                <li class="nav-item">
                    <a href="config/index.html" class="nav-link">⚙️ Configuration</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle" aria-expanded="false">▶</button>
                    <a href="tools/index.html" class="nav-link">🛠️ Development Tools</a>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="tools/debugging.html" class="nav-link">Debugging</a>
                        </li>
                        <li class="nav-item">
                            <a href="tools/profiling.html" class="nav-link">Profiling</a>
                        </li>
                        <li class="nav-item">
                            <a href="tools/testing.html" class="nav-link">Testing</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <span>🏠 Home</span>
            </nav>

            <!-- Hero Section -->
            <section class="content-section">
                <div class="section-header">
                    <h1>Linux Kernel Documentation</h1>
                </div>
                <div class="section-content">
                    <p>
                        Welcome to the comprehensive documentation for the Linux kernel codebase. 
                        This documentation covers all major subsystems, APIs, coding patterns, and 
                        architectural details of the Linux kernel version 6.16.0-rc4.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🚀 Quick Start</h3>
                        </div>
                        <p>
                            New to kernel development? Start with the 
                            <a href="subsystems/kernel/index.html">Core Kernel</a> documentation 
                            to understand the fundamental concepts, then explore specific subsystems 
                            based on your interests.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Architecture Overview -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🏗️ Kernel Architecture Overview</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <p>
                        The Linux kernel is a monolithic, modular operating system kernel that serves 
                        as the core interface between computer hardware and user applications. It provides 
                        essential services including process management, memory management, file system 
                        support, and device drivers.
                    </p>
                    
                    <h3>🔧 Core Design Principles</h3>
                    <ul>
                        <li><strong>Monolithic Architecture:</strong> All kernel services run in a single address space</li>
                        <li><strong>Modular Design:</strong> Support for loadable kernel modules (LKMs)</li>
                        <li><strong>Portability:</strong> Support for multiple hardware architectures</li>
                        <li><strong>Scalability:</strong> Efficient operation from embedded systems to supercomputers</li>
                        <li><strong>Security:</strong> Strong isolation between user and kernel space</li>
                    </ul>
                    
                    <h3>📊 System Architecture Diagram</h3>
                    <div class="architecture-diagram">
                        <pre class="architecture-ascii">
┌─────────────────────────────────────────────────────────────┐
│                    User Space Applications                   │
├─────────────────────────────────────────────────────────────┤
│                     System Call Interface                   │
├─────────────────────────────────────────────────────────────┤
│  Process    │  Memory     │  File       │  Network    │ I/O │
│  Management │  Management │  Systems    │  Stack      │ Mgmt│
├─────────────┼─────────────┼─────────────┼─────────────┼─────┤
│             │             │             │             │     │
│  Scheduler  │   Virtual   │     VFS     │   TCP/IP    │ Dev │
│             │   Memory    │             │   Stack     │ Drv │
│             │             │             │             │     │
├─────────────┴─────────────┴─────────────┴─────────────┴─────┤
│                    Architecture Layer                       │
├─────────────────────────────────────────────────────────────┤
│                      Hardware                               │
└─────────────────────────────────────────────────────────────┘
                        </pre>
                    </div>
                </div>
            </section>

            <!-- Major Subsystems -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔧 Major Subsystems</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">💾 Memory Management (mm/)</h3>
                                <p class="card-subtitle">Virtual memory, page allocation, swap</p>
                            </div>
                            <p>
                                Manages system memory including virtual memory mapping, page allocation 
                                through the buddy allocator, slab/slub allocators for kernel objects, 
                                and memory reclaim mechanisms.
                            </p>
                            <p><strong>Key Components:</strong></p>
                            <ul>
                                <li>Page allocator (buddy system)</li>
                                <li>Slab/SLUB allocators</li>
                                <li>Virtual memory areas (VMAs)</li>
                                <li>Memory reclaim and swap</li>
                                <li>NUMA memory management</li>
                            </ul>
                            <a href="subsystems/mm/index.html" class="nav-link">Explore Memory Management →</a>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">⚙️ Core Kernel (kernel/)</h3>
                                <p class="card-subtitle">Process management, scheduling, synchronization</p>
                            </div>
                            <p>
                                Contains the fundamental kernel functionality including process creation 
                                and management, the scheduler, system calls, interrupt handling, and 
                                synchronization primitives.
                            </p>
                            <p><strong>Key Components:</strong></p>
                            <ul>
                                <li>Process scheduler (CFS)</li>
                                <li>System call interface</li>
                                <li>Interrupt handling</li>
                                <li>Locking primitives</li>
                                <li>Module system</li>
                            </ul>
                            <a href="subsystems/kernel/index.html" class="nav-link">Explore Core Kernel →</a>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">📁 File Systems (fs/)</h3>
                                <p class="card-subtitle">VFS layer, file system implementations</p>
                            </div>
                            <p>
                                Provides the Virtual File System (VFS) layer and implementations of 
                                various file systems including ext4, Btrfs, XFS, and network file systems.
                            </p>
                            <p><strong>Key Components:</strong></p>
                            <ul>
                                <li>Virtual File System (VFS)</li>
                                <li>File system implementations</li>
                                <li>Page cache</li>
                                <li>Directory entry cache</li>
                                <li>File locking</li>
                            </ul>
                            <a href="subsystems/fs/index.html" class="nav-link">Explore File Systems →</a>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🌐 Networking (net/)</h3>
                                <p class="card-subtitle">TCP/IP stack, socket layer, protocols</p>
                            </div>
                            <p>
                                Implements the complete networking stack including TCP/IP protocols, 
                                socket interface, network device drivers, and advanced networking features.
                            </p>
                            <p><strong>Key Components:</strong></p>
                            <ul>
                                <li>Socket layer</li>
                                <li>TCP/IP protocol stack</li>
                                <li>Network device interface</li>
                                <li>Netfilter framework</li>
                                <li>Network namespaces</li>
                            </ul>
                            <a href="subsystems/net/index.html" class="nav-link">Explore Networking →</a>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🔌 Device Drivers (drivers/)</h3>
                                <p class="card-subtitle">Hardware abstraction, device support</p>
                            </div>
                            <p>
                                Contains device drivers for all supported hardware including block devices, 
                                character devices, network devices, and platform-specific drivers.
                            </p>
                            <p><strong>Key Components:</strong></p>
                            <ul>
                                <li>Device model framework</li>
                                <li>Bus drivers (PCI, USB, I2C)</li>
                                <li>Block device drivers</li>
                                <li>Character device drivers</li>
                                <li>Network device drivers</li>
                            </ul>
                            <a href="subsystems/drivers/index.html" class="nav-link">Explore Device Drivers →</a>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🏗️ Architecture Support (arch/)</h3>
                                <p class="card-subtitle">Platform-specific implementations</p>
                            </div>
                            <p>
                                Architecture-specific code for different CPU architectures including 
                                x86, ARM, PowerPC, RISC-V, and many others.
                            </p>
                            <p><strong>Key Components:</strong></p>
                            <ul>
                                <li>Boot and initialization</li>
                                <li>Memory management setup</li>
                                <li>Interrupt handling</li>
                                <li>System call entry/exit</li>
                                <li>CPU-specific features</li>
                            </ul>
                            <a href="arch/index.html" class="nav-link">Explore Architecture Support →</a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Key Features -->
            <section class="content-section">
                <div class="section-header">
                    <h2>✨ Key Features</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="features-grid">
                        <div class="feature-item">
                            <h4>🔍 Comprehensive Coverage</h4>
                            <p>Documentation for every major subsystem, API, and coding pattern in the kernel.</p>
                        </div>
                        <div class="feature-item">
                            <h4>🎨 Multiple Themes</h4>
                            <p>Light, medium, and dark themes for comfortable reading in any environment.</p>
                        </div>
                        <div class="feature-item">
                            <h4>🔎 Advanced Search</h4>
                            <p>Full-text search with intelligent ranking and filtering capabilities.</p>
                        </div>
                        <div class="feature-item">
                            <h4>📱 Responsive Design</h4>
                            <p>Optimized for desktop, tablet, and mobile devices.</p>
                        </div>
                        <div class="feature-item">
                            <h4>♿ Accessibility</h4>
                            <p>WCAG 2.1 compliant with keyboard navigation and screen reader support.</p>
                        </div>
                        <div class="feature-item">
                            <h4>🔗 Cross-References</h4>
                            <p>Extensive linking between related modules and subsystems.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Getting Started -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🚀 Getting Started</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="getting-started-steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4>Choose Your Path</h4>
                                <p>Are you interested in a specific subsystem? Start with:</p>
                                <ul>
                                    <li><strong>Memory Management:</strong> For understanding how Linux manages system memory</li>
                                    <li><strong>File Systems:</strong> For storage and file operations</li>
                                    <li><strong>Networking:</strong> For network protocols and communication</li>
                                    <li><strong>Device Drivers:</strong> For hardware interaction</li>
                                </ul>
                            </div>
                        </div>

                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4>Explore the APIs</h4>
                                <p>Each subsystem has comprehensive API documentation including:</p>
                                <ul>
                                    <li>Function signatures and parameters</li>
                                    <li>Return values and error codes</li>
                                    <li>Usage examples and patterns</li>
                                    <li>Cross-references to related functions</li>
                                </ul>
                            </div>
                        </div>

                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4>Study Coding Patterns</h4>
                                <p>Learn kernel-specific programming patterns:</p>
                                <ul>
                                    <li>Memory allocation and management</li>
                                    <li>Locking and synchronization</li>
                                    <li>Error handling conventions</li>
                                    <li>Module initialization patterns</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Statistics -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📊 Kernel Statistics</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">6.16.0-rc4</div>
                            <div class="stat-label">Kernel Version</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">30M+</div>
                            <div class="stat-label">Lines of Code</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">25+</div>
                            <div class="stat-label">Architectures</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">1000+</div>
                            <div class="stat-label">Contributors</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">Subsystems</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">5000+</div>
                            <div class="stat-label">Device Drivers</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Footer -->
            <footer class="footer">
                <div class="footer-content">
                    <div class="footer-section">
                        <h4>Documentation</h4>
                        <ul>
                            <li><a href="subsystems/index.html">Core Subsystems</a></li>
                            <li><a href="api/index.html">API Reference</a></li>
                            <li><a href="patterns/index.html">Coding Patterns</a></li>
                            <li><a href="config/index.html">Configuration</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>Architecture</h4>
                        <ul>
                            <li><a href="arch/x86/index.html">x86/x86_64</a></li>
                            <li><a href="arch/arm/index.html">ARM</a></li>
                            <li><a href="arch/arm64/index.html">ARM64</a></li>
                            <li><a href="arch/riscv/index.html">RISC-V</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>Tools</h4>
                        <ul>
                            <li><a href="tools/debugging.html">Debugging</a></li>
                            <li><a href="tools/profiling.html">Profiling</a></li>
                            <li><a href="tools/testing.html">Testing</a></li>
                            <li><a href="tools/build.html">Build System</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>About</h4>
                        <p>
                            This documentation is generated from the Linux kernel source code
                            and is licensed under GPL-2.0. Last updated: January 2024.
                        </p>
                        <p>
                            <a href="https://github.com/torvalds/linux" target="_blank" rel="noopener">
                                View on GitHub
                            </a>
                        </p>
                    </div>
                </div>
            </footer>
        </main>
    </div>

    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/search.js"></script>
    <script src="assets/js/syntax.js"></script>
</body>
</html>
