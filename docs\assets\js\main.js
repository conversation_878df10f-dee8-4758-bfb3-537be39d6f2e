/**
 * Linux Kernel Documentation - Main JavaScript
 * Handles theme switching, navigation, and interactive features
 */

class KernelDocs {
  constructor() {
    this.currentTheme = localStorage.getItem('kernel-docs-theme') || 'light';
    this.searchIndex = null;
    this.searchResults = [];
    
    this.init();
  }

  init() {
    this.initTheme();
    this.initNavigation();
    this.initSearch();
    this.initCollapsibleSections();
    this.initCodeBlocks();
    this.initAccessibility();
    this.initAnimations();
  }

  // Theme Management
  initTheme() {
    document.documentElement.setAttribute('data-theme', this.currentTheme);
    
    const themeButton = document.querySelector('.theme-button');
    const themeDropdown = document.querySelector('.theme-dropdown');
    const themeOptions = document.querySelectorAll('.theme-option');

    if (themeButton) {
      themeButton.textContent = this.capitalizeFirst(this.currentTheme);
      
      themeButton.addEventListener('click', (e) => {
        e.stopPropagation();
        themeDropdown.classList.toggle('show');
      });
    }

    if (themeOptions) {
      themeOptions.forEach(option => {
        option.addEventListener('click', (e) => {
          const theme = e.target.dataset.theme;
          this.setTheme(theme);
          themeDropdown.classList.remove('show');
        });
      });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', () => {
      if (themeDropdown) {
        themeDropdown.classList.remove('show');
      }
    });
  }

  setTheme(theme) {
    this.currentTheme = theme;
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('kernel-docs-theme', theme);
    
    const themeButton = document.querySelector('.theme-button');
    if (themeButton) {
      themeButton.textContent = this.capitalizeFirst(theme);
    }
  }

  // Navigation
  initNavigation() {
    const navToggles = document.querySelectorAll('.nav-toggle');
    const navLinks = document.querySelectorAll('.nav-link');

    navToggles.forEach(toggle => {
      toggle.addEventListener('click', (e) => {
        e.preventDefault();
        const submenu = toggle.parentElement.querySelector('.nav-submenu');
        if (submenu) {
          submenu.classList.toggle('expanded');
          toggle.classList.toggle('expanded');
        }
      });
    });

    // Highlight current page
    const currentPath = window.location.pathname;
    navLinks.forEach(link => {
      if (link.getAttribute('href') === currentPath) {
        link.classList.add('active');
        // Expand parent menus
        let parent = link.closest('.nav-submenu');
        while (parent) {
          parent.classList.add('expanded');
          const toggle = parent.parentElement.querySelector('.nav-toggle');
          if (toggle) {
            toggle.classList.add('expanded');
          }
          parent = parent.parentElement.closest('.nav-submenu');
        }
      }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }

  // Search Functionality
  initSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchResults = document.querySelector('.search-results');

    if (!searchInput || !searchResults) return;

    let searchTimeout;

    searchInput.addEventListener('input', (e) => {
      clearTimeout(searchTimeout);
      const query = e.target.value.trim();

      if (query.length < 2) {
        searchResults.style.display = 'none';
        return;
      }

      searchTimeout = setTimeout(() => {
        this.performSearch(query);
      }, 300);
    });

    searchInput.addEventListener('focus', () => {
      if (searchInput.value.trim().length >= 2) {
        searchResults.style.display = 'block';
      }
    });

    // Close search results when clicking outside
    document.addEventListener('click', (e) => {
      if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
        searchResults.style.display = 'none';
      }
    });

    // Handle keyboard navigation in search results
    searchInput.addEventListener('keydown', (e) => {
      const items = searchResults.querySelectorAll('.search-result');
      const current = searchResults.querySelector('.search-result.highlighted');
      let index = Array.from(items).indexOf(current);

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          index = Math.min(index + 1, items.length - 1);
          this.highlightSearchResult(items, index);
          break;
        case 'ArrowUp':
          e.preventDefault();
          index = Math.max(index - 1, 0);
          this.highlightSearchResult(items, index);
          break;
        case 'Enter':
          e.preventDefault();
          if (current) {
            current.click();
          }
          break;
        case 'Escape':
          searchResults.style.display = 'none';
          searchInput.blur();
          break;
      }
    });
  }

  performSearch(query) {
    // Simple client-side search implementation
    // In a real implementation, this would use a proper search index
    const results = this.searchContent(query);
    this.displaySearchResults(results);
  }

  searchContent(query) {
    const results = [];
    const searchTerms = query.toLowerCase().split(' ');
    
    // Search in page content
    const contentElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, code, .api-signature');
    
    contentElements.forEach(element => {
      const text = element.textContent.toLowerCase();
      const matches = searchTerms.every(term => text.includes(term));
      
      if (matches) {
        results.push({
          title: this.getElementTitle(element),
          content: this.truncateText(element.textContent, 100),
          url: this.getElementUrl(element),
          type: this.getElementType(element)
        });
      }
    });

    return results.slice(0, 10); // Limit to 10 results
  }

  displaySearchResults(results) {
    const searchResults = document.querySelector('.search-results');
    
    if (results.length === 0) {
      searchResults.innerHTML = '<div class="search-result">No results found</div>';
    } else {
      searchResults.innerHTML = results.map(result => `
        <div class="search-result" data-url="${result.url}">
          <div class="search-result-title">${result.title}</div>
          <div class="search-result-content">${result.content}</div>
          <div class="search-result-type">${result.type}</div>
        </div>
      `).join('');

      // Add click handlers
      searchResults.querySelectorAll('.search-result').forEach(item => {
        item.addEventListener('click', () => {
          const url = item.dataset.url;
          if (url.startsWith('#')) {
            const target = document.querySelector(url);
            if (target) {
              target.scrollIntoView({ behavior: 'smooth' });
              searchResults.style.display = 'none';
            }
          } else {
            window.location.href = url;
          }
        });
      });
    }

    searchResults.style.display = 'block';
  }

  highlightSearchResult(items, index) {
    items.forEach(item => item.classList.remove('highlighted'));
    if (items[index]) {
      items[index].classList.add('highlighted');
    }
  }

  // Collapsible Sections
  initCollapsibleSections() {
    const sectionToggles = document.querySelectorAll('.section-toggle');
    
    sectionToggles.forEach(toggle => {
      toggle.addEventListener('click', () => {
        const content = toggle.closest('.content-section').querySelector('.section-content');
        if (content) {
          content.classList.toggle('collapsed');
          toggle.textContent = content.classList.contains('collapsed') ? '▶' : '▼';
        }
      });
    });
  }

  // Code Block Enhancements
  initCodeBlocks() {
    const codeBlocks = document.querySelectorAll('pre code');
    
    codeBlocks.forEach(block => {
      // Add copy button
      const copyButton = document.createElement('button');
      copyButton.className = 'code-copy';
      copyButton.textContent = 'Copy';
      copyButton.addEventListener('click', () => {
        navigator.clipboard.writeText(block.textContent).then(() => {
          copyButton.textContent = 'Copied!';
          setTimeout(() => {
            copyButton.textContent = 'Copy';
          }, 2000);
        });
      });

      // Wrap in code block container
      const container = document.createElement('div');
      container.className = 'code-block';
      
      const header = document.createElement('div');
      header.className = 'code-header';
      header.innerHTML = `
        <span class="code-language">C</span>
      `;
      header.appendChild(copyButton);

      block.parentElement.parentNode.insertBefore(container, block.parentElement);
      container.appendChild(header);
      container.appendChild(block.parentElement);
      container.classList.add('has-header');
    });
  }

  // Accessibility
  initAccessibility() {
    // Add skip link
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.className = 'sr-only';
    skipLink.textContent = 'Skip to main content';
    document.body.insertBefore(skipLink, document.body.firstChild);

    // Improve focus management
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });

    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }

  // Animations
  initAnimations() {
    // Fade in animation for content
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('fade-in');
        }
      });
    }, observerOptions);

    document.querySelectorAll('.content-section, .card').forEach(el => {
      observer.observe(el);
    });
  }

  // Utility Methods
  capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  truncateText(text, length) {
    return text.length > length ? text.substring(0, length) + '...' : text;
  }

  getElementTitle(element) {
    if (element.tagName.match(/^H[1-6]$/)) {
      return element.textContent;
    }
    const heading = element.closest('section')?.querySelector('h1, h2, h3, h4, h5, h6');
    return heading ? heading.textContent : 'Documentation';
  }

  getElementUrl(element) {
    const id = element.id || element.closest('[id]')?.id;
    return id ? `#${id}` : window.location.pathname;
  }

  getElementType(element) {
    if (element.classList.contains('api-signature')) return 'API';
    if (element.tagName === 'CODE') return 'Code';
    if (element.tagName.match(/^H[1-6]$/)) return 'Section';
    return 'Content';
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new KernelDocs();
});
