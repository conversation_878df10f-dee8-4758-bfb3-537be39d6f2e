# Linux Kernel Documentation

This directory contains comprehensive HTML documentation for the Linux kernel codebase, automatically generated from source code analysis.

## Structure

```
docs/
├── index.html                 # Main documentation hub
├── assets/
│   ├── css/
│   │   ├── main.css          # Main stylesheet with themes
│   │   └── syntax.css        # Code syntax highlighting
│   ├── js/
│   │   ├── main.js           # Core functionality
│   │   ├── search.js         # Search implementation
│   │   └── syntax.js         # Syntax highlighting
│   └── images/               # Documentation images
├── subsystems/
│   ├── mm/                   # Memory management
│   ├── kernel/               # Core kernel
│   ├── fs/                   # File systems
│   ├── net/                  # Networking
│   ├── drivers/              # Device drivers
│   └── arch/                 # Architecture-specific
├── api/                      # API reference
├── patterns/                 # Coding patterns analysis
└── config/                   # Configuration options
```

## Features

- **Responsive Design**: Works on desktop, tablet, and mobile
- **Multiple Themes**: Light, medium (gray), and dark themes
- **Search Functionality**: Client-side search across all documentation
- **Cross-References**: Links between related modules and subsystems
- **Syntax Highlighting**: C code with proper highlighting
- **Accessibility**: WCAG 2.1 compliant
- **Interactive Elements**: Collapsible sections, smooth animations

## Navigation

- Use the main index.html as the entry point
- Navigate through subsystems using the hierarchical menu
- Use breadcrumb navigation to track your location
- Search functionality is available on every page

## Themes

Three themes are available:
- **Light**: Clean white background with dark text
- **Medium**: Professional gray theme
- **Dark**: Dark background with light text

Switch themes using the theme selector in the top navigation.

## File Naming Conventions

- Use kebab-case for HTML files (e.g., `memory-management.html`)
- Use descriptive names that match the kernel structure
- API reference files use the format `api-[subsystem].html`
- Configuration files use the format `config-[area].html`

## Building

This documentation is generated automatically from the kernel source code. To regenerate:

1. Ensure you have the complete Linux kernel source
2. Run the documentation generator
3. The HTML files will be updated with the latest information

## Contributing

When adding new documentation:
1. Follow the established HTML structure
2. Use semantic HTML5 elements
3. Ensure accessibility compliance
4. Test with all three themes
5. Validate cross-references work correctly

## Browser Support

- Chrome/Chromium 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## License

This documentation follows the same license as the Linux kernel (GPL-2.0).
