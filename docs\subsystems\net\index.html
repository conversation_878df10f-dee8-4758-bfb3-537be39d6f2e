<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive documentation for Linux kernel networking subsystem (net/)">
    <meta name="keywords" content="Linux kernel, networking, TCP/IP, socket layer, netfilter, packet handling, network devices">
    
    <title>Networking (net/) - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐧</text></svg>">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="../../index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="../../index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle expanded" aria-expanded="true">▼</button>
                    <a href="../index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu expanded">
                        <li class="nav-item">
                            <a href="../mm/index.html" class="nav-link">💾 Memory Management (mm/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../kernel/index.html" class="nav-link">⚙️ Core Kernel (kernel/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../fs/index.html" class="nav-link">📁 File Systems (fs/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="index.html" class="nav-link active">🌐 Networking (net/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../drivers/index.html" class="nav-link">🔌 Device Drivers (drivers/)</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="../../patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
                
                <li class="nav-item">
                    <a href="../../api/index.html" class="nav-link">📚 API Reference</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="../../index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="../index.html">Core Subsystems</a>
                <span class="breadcrumb-separator">›</span>
                <span>Networking (net/)</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>🌐 Networking Subsystem (net/)</h1>
                </div>
                <div class="section-content">
                    <p>
                        The networking subsystem provides comprehensive network communication 
                        capabilities for the Linux kernel. It implements the complete TCP/IP 
                        stack, socket layer, network device management, packet filtering, 
                        and traffic control mechanisms.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🎯 Core Responsibilities</h3>
                        </div>
                        <ul>
                            <li><strong>Socket Layer:</strong> BSD socket API implementation and socket management</li>
                            <li><strong>Protocol Stack:</strong> TCP/IP, UDP, ICMP, and other protocol implementations</li>
                            <li><strong>Network Devices:</strong> Network interface management and device drivers</li>
                            <li><strong>Packet Processing:</strong> Packet reception, transmission, and routing</li>
                            <li><strong>Netfilter:</strong> Packet filtering, NAT, and connection tracking</li>
                            <li><strong>Traffic Control:</strong> Quality of Service (QoS) and bandwidth management</li>
                            <li><strong>Network Namespaces:</strong> Network isolation and virtualization</li>
                            <li><strong>Wireless Support:</strong> 802.11 wireless networking and cfg80211</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Architecture Overview -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🏗️ Network Stack Architecture</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="architecture-diagram">
                        <pre class="architecture-ascii">
┌─────────────────────────────────────────────────────────────┐
│                    User Space Applications                   │
├─────────────────────────────────────────────────────────────┤
│                     Socket Layer (BSD API)                  │
│                  (socket, bind, listen, accept)             │
├─────────────────────────────────────────────────────────────┤
│                    Transport Layer                          │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │     TCP     │     UDP     │    SCTP     │   Others    │  │
│  │ (Reliable)  │ (Datagram)  │ (Stream)    │             │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                     Network Layer                           │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │    IPv4     │    IPv6     │    ICMP     │   Routing   │  │
│  │             │             │             │   Tables    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    Netfilter Framework                      │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │  Packet     │   Connection│     NAT     │  Firewall   │  │
│  │ Filtering   │   Tracking  │             │    Rules    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                     Link Layer                              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │  Ethernet   │   Wireless  │  Bridge     │   VLAN      │  │
│  │             │   (802.11)  │             │             │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                   Network Devices                           │
│                 (eth0, wlan0, lo, etc.)                     │
└─────────────────────────────────────────────────────────────┘
                        </pre>
                    </div>
                    
                    <h3>🔧 Core Components</h3>
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Socket Layer</h4>
                                <p class="card-subtitle">BSD socket API implementation</p>
                            </div>
                            <p>
                                Provides the standard socket interface for network communication, 
                                implementing socket creation, binding, listening, and data transfer.
                            </p>
                            <p><strong>Key Files:</strong> <code>socket.c</code>, <code>af_inet.c</code>, <code>af_unix.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">TCP/IP Stack</h4>
                                <p class="card-subtitle">Protocol implementations</p>
                            </div>
                            <p>
                                Complete implementation of TCP, UDP, IP, ICMP, and other network 
                                protocols with congestion control, flow control, and error handling.
                            </p>
                            <p><strong>Key Directories:</strong> <code>ipv4/</code>, <code>ipv6/</code>, <code>core/</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Network Devices</h4>
                                <p class="card-subtitle">Interface management</p>
                            </div>
                            <p>
                                Manages network interfaces, device registration, configuration, 
                                and provides the interface between protocols and device drivers.
                            </p>
                            <p><strong>Key Files:</strong> <code>core/dev.c</code>, <code>core/net-sysfs.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Netfilter</h4>
                                <p class="card-subtitle">Packet filtering framework</p>
                            </div>
                            <p>
                                Provides hooks for packet filtering, network address translation (NAT), 
                                connection tracking, and firewall functionality.
                            </p>
                            <p><strong>Key Directory:</strong> <code>netfilter/</code></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Socket Layer -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔌 Socket Layer</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>struct socket</h3>
                        <div class="api-signature">
                            <code>struct socket</code> - Represents a network socket
                        </div>
                        <p>
                            The socket structure represents a network endpoint for communication 
                            and contains the socket state, operations, and associated data.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct socket {
    socket_state state;           /* Socket state (SS_CONNECTED, etc.) */
    short type;                   /* Socket type (SOCK_STREAM, etc.) */
    unsigned long flags;          /* Socket flags */
    
    struct file *file;            /* Associated file structure */
    struct sock *sk;              /* Network layer representation */
    const struct proto_ops *ops;  /* Protocol operations */
    
    struct socket_wq wq;          /* Wait queue */
};</code></pre>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>struct sock</h3>
                        <div class="api-signature">
                            <code>struct sock</code> - Network layer socket representation
                        </div>
                        <p>
                            The sock structure represents the network layer view of a socket 
                            and contains protocol-specific information and state.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct sock {
    struct sock_common __sk_common; /* Common socket fields */
    
    /* Socket state */
    unsigned int sk_state;        /* Socket state */
    unsigned short sk_type;       /* Socket type */
    unsigned short sk_protocol;   /* Protocol */
    
    /* Reference counting */
    refcount_t sk_refcnt;         /* Reference count */
    
    /* Socket options */
    unsigned int sk_flags;        /* Socket flags */
    unsigned int sk_lingertime;   /* Linger time */
    
    /* Buffer management */
    int sk_rcvbuf;                /* Receive buffer size */
    int sk_sndbuf;                /* Send buffer size */
    atomic_t sk_rmem_alloc;       /* Receive memory allocated */
    atomic_t sk_wmem_alloc;       /* Write memory allocated */
    
    /* Queue management */
    struct sk_buff_head sk_receive_queue; /* Receive queue */
    struct sk_buff_head sk_write_queue;   /* Write queue */
    struct sk_buff_head sk_error_queue;   /* Error queue */
    
    /* Protocol operations */
    struct proto *sk_prot;        /* Protocol operations */
    
    /* Socket callbacks */
    void (*sk_state_change)(struct sock *sk);
    void (*sk_data_ready)(struct sock *sk);
    void (*sk_write_space)(struct sock *sk);
    void (*sk_error_report)(struct sock *sk);
    
    /* Timers */
    struct timer_list sk_timer;   /* Socket timer */
    
    /* Security */
    void *sk_security;            /* Security context */
    
    /* Socket address */
    struct sockaddr_storage sk_addr; /* Socket address */
};</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- TCP Implementation -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🚀 TCP Implementation</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>TCP Socket APIs</h3>
                        
                        <div class="api-function">
                            <div class="api-signature">
                                <code>int tcp_connect(struct sock *sk)</code>
                            </div>
                            <p><strong>Purpose:</strong> Initiate a TCP connection</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* TCP connection establishment */
static int establish_tcp_connection(struct sockaddr_in *addr)
{
    struct socket *sock;
    int ret;
    
    /* Create TCP socket */
    ret = sock_create(AF_INET, SOCK_STREAM, IPPROTO_TCP, &sock);
    if (ret < 0) {
        pr_err("Failed to create socket: %d\n", ret);
        return ret;
    }
    
    /* Connect to remote address */
    ret = sock->ops->connect(sock, (struct sockaddr *)addr, 
                            sizeof(*addr), O_RDWR);
    if (ret < 0) {
        pr_err("Failed to connect: %d\n", ret);
        sock_release(sock);
        return ret;
    }
    
    pr_info("TCP connection established\n");
    return 0;
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>TCP Congestion Control</h3>
                        <p>
                            Linux implements multiple TCP congestion control algorithms 
                            to optimize network performance under different conditions.
                        </p>
                        
                        <div class="config-grid">
                            <div class="config-item">
                                <h4>Cubic (Default)</h4>
                                <p>Optimized for high-speed, long-distance networks</p>
                            </div>
                            <div class="config-item">
                                <h4>BBR</h4>
                                <p>Bottleneck Bandwidth and RTT-based congestion control</p>
                            </div>
                            <div class="config-item">
                                <h4>Reno</h4>
                                <p>Traditional TCP congestion control algorithm</p>
                            </div>
                            <div class="config-item">
                                <h4>Vegas</h4>
                                <p>Delay-based congestion control</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/search.js"></script>
    <script src="../../assets/js/syntax.js"></script>
</body>
</html>
