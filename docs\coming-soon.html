<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Documentation coming soon - Linux Kernel Documentation">
    <meta name="keywords" content="Linux kernel, documentation, coming soon">
    
    <title>Coming Soon - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐧</text></svg>">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle" aria-expanded="false">▶</button>
                    <a href="subsystems/index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="subsystems/mm/index.html" class="nav-link">💾 Memory Management</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
                
                <li class="nav-item">
                    <a href="api/index.html" class="nav-link">📚 API Reference</a>
                </li>
                
                <li class="nav-item">
                    <a href="config/index.html" class="nav-link">⚙️ Configuration</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <span>Coming Soon</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>🚧 Documentation Coming Soon</h1>
                </div>
                <div class="section-content">
                    <div class="coming-soon-container">
                        <div class="coming-soon-icon">
                            🚧
                        </div>
                        
                        <h2>This section is under development</h2>
                        
                        <p>
                            We're working hard to bring you comprehensive documentation for this 
                            part of the Linux kernel. In the meantime, you can explore the 
                            available documentation sections.
                        </p>
                        
                        <div class="available-sections">
                            <h3>📚 Available Now</h3>
                            <div class="section-links">
                                <a href="subsystems/mm/index.html" class="section-link">
                                    <div class="section-icon">💾</div>
                                    <div class="section-info">
                                        <h4>Memory Management</h4>
                                        <p>Complete documentation for the mm/ subsystem</p>
                                    </div>
                                </a>
                                
                                <a href="patterns/index.html" class="section-link">
                                    <div class="section-icon">🎯</div>
                                    <div class="section-info">
                                        <h4>Coding Patterns</h4>
                                        <p>Essential kernel programming patterns and best practices</p>
                                    </div>
                                </a>
                                
                                <a href="api/index.html" class="section-link">
                                    <div class="section-icon">📚</div>
                                    <div class="section-info">
                                        <h4>API Reference</h4>
                                        <p>Overview of kernel APIs (detailed docs coming soon)</p>
                                    </div>
                                </a>
                                
                                <a href="config/index.html" class="section-link">
                                    <div class="section-icon">⚙️</div>
                                    <div class="section-info">
                                        <h4>Configuration</h4>
                                        <p>Kernel configuration system and options</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                        
                        <div class="help-section">
                            <h3>💡 How You Can Help</h3>
                            <p>
                                This documentation is a community effort. If you'd like to contribute 
                                to documenting the Linux kernel, please consider:
                            </p>
                            <ul>
                                <li>Contributing to the official kernel documentation</li>
                                <li>Reporting issues or suggesting improvements</li>
                                <li>Sharing your kernel development experience</li>
                            </ul>
                        </div>
                        
                        <div class="back-navigation">
                            <a href="index.html" class="back-button">
                                ← Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/search.js"></script>
    <script src="assets/js/syntax.js"></script>
    
    <style>
        .coming-soon-container {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .coming-soon-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .available-sections {
            margin: 3rem 0;
            text-align: left;
        }
        
        .section-links {
            display: grid;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .section-link {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.2s ease;
        }
        
        .section-link:hover {
            background: var(--bg-tertiary);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-medium);
        }
        
        .section-icon {
            font-size: 2rem;
            margin-right: 1rem;
        }
        
        .section-info h4 {
            margin-bottom: 0.25rem;
            color: var(--accent-primary);
        }
        
        .section-info p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .help-section {
            margin: 3rem 0;
            padding: 1.5rem;
            background: var(--bg-secondary);
            border-radius: 8px;
            text-align: left;
        }
        
        .help-section ul {
            margin-top: 1rem;
        }
        
        .back-button {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: var(--accent-primary);
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 600;
            transition: background-color 0.2s ease;
        }
        
        .back-button:hover {
            background: var(--accent-secondary);
            color: white;
        }
    </style>
</body>
</html>
