<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Linux kernel API reference documentation">
    <meta name="keywords" content="Linux kernel, API reference, functions, data structures, interfaces">
    
    <title>API Reference - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐧</text></svg>">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle" aria-expanded="false">▶</button>
                    <a href="../subsystems/index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="../subsystems/mm/index.html" class="nav-link">💾 Memory Management</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle expanded" aria-expanded="true">▼</button>
                    <a href="index.html" class="nav-link active">📚 API Reference</a>
                    <ul class="nav-submenu expanded">
                        <li class="nav-item">
                            <a href="#memory" class="nav-link">Memory APIs - Coming Soon</a>
                        </li>
                        <li class="nav-item">
                            <a href="#filesystem" class="nav-link">Filesystem APIs - Coming Soon</a>
                        </li>
                        <li class="nav-item">
                            <a href="#networking" class="nav-link">Networking APIs - Coming Soon</a>
                        </li>
                        <li class="nav-item">
                            <a href="#driver" class="nav-link">Driver APIs - Coming Soon</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="../patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="../index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <span>API Reference</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>📚 Linux Kernel API Reference</h1>
                </div>
                <div class="section-content">
                    <p>
                        Comprehensive API reference documentation for Linux kernel functions, 
                        data structures, and interfaces. This section provides detailed information 
                        about function signatures, parameters, return values, and usage examples.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🎯 What You'll Find Here</h3>
                        </div>
                        <ul>
                            <li><strong>Function Signatures:</strong> Complete prototypes with parameter types</li>
                            <li><strong>Parameter Documentation:</strong> Detailed description of each parameter</li>
                            <li><strong>Return Values:</strong> Possible return values and error codes</li>
                            <li><strong>Usage Examples:</strong> Real-world code examples</li>
                            <li><strong>Cross-References:</strong> Links to related functions and subsystems</li>
                            <li><strong>Version Information:</strong> When functions were introduced or changed</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- API Categories -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📋 API Categories</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-categories">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">💾 Memory Management APIs</h3>
                                <p class="card-subtitle">🚧 Coming Soon</p>
                            </div>
                            <p>Memory allocation, deallocation, and management functions.</p>
                            <p><strong>Will Include:</strong></p>
                            <ul>
                                <li><code>kmalloc()</code>, <code>kfree()</code> - General memory allocation</li>
                                <li><code>alloc_pages()</code>, <code>free_pages()</code> - Page allocation</li>
                                <li><code>vmalloc()</code>, <code>vfree()</code> - Virtual memory allocation</li>
                                <li><code>get_user()</code>, <code>put_user()</code> - User space access</li>
                                <li>SLUB allocator functions</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">📁 File System APIs</h3>
                                <p class="card-subtitle">🚧 Coming Soon</p>
                            </div>
                            <p>VFS layer and file system operation functions.</p>
                            <p><strong>Will Include:</strong></p>
                            <ul>
                                <li><code>filp_open()</code>, <code>filp_close()</code> - File operations</li>
                                <li><code>vfs_read()</code>, <code>vfs_write()</code> - VFS operations</li>
                                <li><code>d_alloc()</code>, <code>dput()</code> - Dentry operations</li>
                                <li><code>iget()</code>, <code>iput()</code> - Inode operations</li>
                                <li>Page cache functions</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🌐 Networking APIs</h3>
                                <p class="card-subtitle">🚧 Coming Soon</p>
                            </div>
                            <p>Socket layer and network protocol functions.</p>
                            <p><strong>Will Include:</strong></p>
                            <ul>
                                <li><code>sock_create()</code>, <code>sock_release()</code> - Socket operations</li>
                                <li><code>alloc_skb()</code>, <code>kfree_skb()</code> - Socket buffer management</li>
                                <li><code>netif_rx()</code>, <code>netif_receive_skb()</code> - Network interface</li>
                                <li><code>dev_queue_xmit()</code> - Packet transmission</li>
                                <li>Protocol registration functions</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🔌 Device Driver APIs</h3>
                                <p class="card-subtitle">🚧 Coming Soon</p>
                            </div>
                            <p>Device registration and hardware abstraction functions.</p>
                            <p><strong>Will Include:</strong></p>
                            <ul>
                                <li><code>request_irq()</code>, <code>free_irq()</code> - Interrupt handling</li>
                                <li><code>ioremap()</code>, <code>iounmap()</code> - Memory mapping</li>
                                <li><code>pci_register_driver()</code> - PCI driver registration</li>
                                <li><code>platform_driver_register()</code> - Platform drivers</li>
                                <li>DMA allocation functions</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🔒 Synchronization APIs</h3>
                                <p class="card-subtitle">🚧 Coming Soon</p>
                            </div>
                            <p>Locking and synchronization primitives.</p>
                            <p><strong>Will Include:</strong></p>
                            <ul>
                                <li><code>spin_lock()</code>, <code>spin_unlock()</code> - Spinlocks</li>
                                <li><code>mutex_lock()</code>, <code>mutex_unlock()</code> - Mutexes</li>
                                <li><code>down()</code>, <code>up()</code> - Semaphores</li>
                                <li><code>rcu_read_lock()</code>, <code>rcu_read_unlock()</code> - RCU</li>
                                <li>Atomic operations</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">⚙️ Core Kernel APIs</h3>
                                <p class="card-subtitle">🚧 Coming Soon</p>
                            </div>
                            <p>Process management and core kernel functions.</p>
                            <p><strong>Will Include:</strong></p>
                            <ul>
                                <li><code>schedule()</code>, <code>yield()</code> - Scheduling</li>
                                <li><code>wake_up()</code>, <code>wait_event()</code> - Wait queues</li>
                                <li><code>copy_from_user()</code>, <code>copy_to_user()</code> - User access</li>
                                <li><code>printk()</code>, <code>pr_info()</code> - Logging</li>
                                <li>Timer and workqueue functions</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Current Status -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📊 Documentation Status</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="status-info">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🚧 Work in Progress</h3>
                            </div>
                            <p>
                                API reference documentation is currently being developed. 
                                In the meantime, you can find API information within the 
                                individual subsystem documentation pages.
                            </p>
                            <p><strong>Available Now:</strong></p>
                            <ul>
                                <li><a href="../subsystems/mm/index.html">Memory Management APIs</a> - In MM subsystem documentation</li>
                                <li><a href="../patterns/index.html">Common API Patterns</a> - In coding patterns documentation</li>
                            </ul>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">📅 Coming Soon</h3>
                            </div>
                            <p>Planned API documentation sections:</p>
                            <ul>
                                <li>Complete function reference with examples</li>
                                <li>Data structure documentation</li>
                                <li>Macro and constant definitions</li>
                                <li>Cross-platform compatibility notes</li>
                                <li>Performance considerations</li>
                                <li>Version history and changes</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/search.js"></script>
    <script src="../assets/js/syntax.js"></script>
</body>
</html>
