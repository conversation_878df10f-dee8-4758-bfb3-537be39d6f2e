// SPDX-License-Identifier: GPL-2.0-or-later OR MIT
/*
 * Author: <PERSON> <<EMAIL>>
 */

/dts-v1/;

#include "bcm4709.dtsi"
#include "bcm5301x-nand-cs0-bch8.dtsi"

#include <dt-bindings/leds/common.h>

/ {
	compatible = "asus,rt-ac3200", "brcm,bcm4709", "brcm,bcm4708";
	model = "ASUS RT-AC3200";

	memory@0 {
		reg = <0x00000000 0x08000000>,
		      <0x88000000 0x08000000>;
		device_type = "memory";
	};

	nvram@1c080000 {
		compatible = "brcm,nvram";
		reg = <0x1c080000 0x00180000>;

		et0macaddr: et0macaddr {
			#nvmem-cell-cells = <1>;
		};
	};

	gpio-keys {
		compatible = "gpio-keys";

		button-reset {
			label = "Reset";
			linux,code = <KEY_RESTART>;
			gpios = <&chipcommon 11 GPIO_ACTIVE_LOW>;
		};

		button-wifi {
			label = "Wi-Fi";
			linux,code = <KEY_RFKILL>;
			gpios = <&chipcommon 4 GPIO_ACTIVE_LOW>;
		};

		button-wps {
			label = "WPS";
			linux,code = <KEY_WPS_BUTTON>;
			gpios = <&chipcommon 7 GPIO_ACTIVE_LOW>;
		};
	};

	leds {
		compatible = "gpio-leds";

		led-power {
			color = <LED_COLOR_ID_WHITE>;
			function = LED_FUNCTION_POWER;
			gpios = <&chipcommon 3 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "default-on";
		};

		led-wan-red {
			color = <LED_COLOR_ID_RED>;
			function = LED_FUNCTION_WAN;
			gpios = <&chipcommon 5 GPIO_ACTIVE_HIGH>;
		};

		led-wps {
			color = <LED_COLOR_ID_WHITE>;
			function = LED_FUNCTION_WPS;
			gpios = <&chipcommon 14 GPIO_ACTIVE_LOW>;
		};
	};
};

&gmac0 {
	nvmem-cells = <&et0macaddr 0>;
	nvmem-cell-names = "mac-address";
};

&gmac1 {
	nvmem-cells = <&et0macaddr 1>;
	nvmem-cell-names = "mac-address";
};

&gmac2 {
	nvmem-cells = <&et0macaddr 2>;
	nvmem-cell-names = "mac-address";
};

&nandcs {
	partitions {
		compatible = "fixed-partitions";
		#address-cells = <1>;
		#size-cells = <1>;

		partition@0 {
			reg = <0x00000000 0x00080000>;
			label = "boot";
			read-only;
		};

		partition@80000 {
			reg = <0x00080000 0x00180000>;
			label = "nvram";
		};

		partition@200000 {
			compatible = "brcm,trx";
			reg = <0x00200000 0x07e00000>;
			label = "firmware";
		};
	};
};

&srab {
	status = "okay";

	ports {
		port@0 {
			label = "wan";
		};

		port@1 {
			label = "lan4";
		};

		port@2 {
			label = "lan3";
		};

		port@3 {
			label = "lan2";
		};

		port@4 {
			label = "lan1";
		};
	};
};

&usb2 {
	vcc-gpio = <&chipcommon 9 GPIO_ACTIVE_HIGH>;
};

&usb3_phy {
	status = "okay";
};
