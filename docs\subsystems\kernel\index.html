<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive documentation for Linux kernel core subsystem (kernel/)">
    <meta name="keywords" content="Linux kernel, core kernel, process management, scheduling, system calls, interrupts, RCU">
    
    <title>Core Kernel (kernel/) - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐧</text></svg>">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="../../index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="../../index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle expanded" aria-expanded="true">▼</button>
                    <a href="../index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu expanded">
                        <li class="nav-item">
                            <a href="../mm/index.html" class="nav-link">💾 Memory Management (mm/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="index.html" class="nav-link active">⚙️ Core Kernel (kernel/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../fs/index.html" class="nav-link">📁 File Systems (fs/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../net/index.html" class="nav-link">🌐 Networking (net/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../drivers/index.html" class="nav-link">🔌 Device Drivers (drivers/)</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="../../patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
                
                <li class="nav-item">
                    <a href="../../api/index.html" class="nav-link">📚 API Reference</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="../../index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="../index.html">Core Subsystems</a>
                <span class="breadcrumb-separator">›</span>
                <span>Core Kernel (kernel/)</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>⚙️ Core Kernel Subsystem (kernel/)</h1>
                </div>
                <div class="section-content">
                    <p>
                        The core kernel subsystem contains the fundamental functionality that makes Linux 
                        an operating system. It provides process management, scheduling, system calls, 
                        interrupt handling, synchronization primitives, and other essential services 
                        that all other kernel subsystems depend upon.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🎯 Core Responsibilities</h3>
                        </div>
                        <ul>
                            <li><strong>Process Management:</strong> Process creation, termination, and lifecycle management</li>
                            <li><strong>Scheduling:</strong> CPU time allocation using the Completely Fair Scheduler (CFS)</li>
                            <li><strong>System Calls:</strong> Interface between user space and kernel space</li>
                            <li><strong>Interrupt Handling:</strong> Hardware and software interrupt processing</li>
                            <li><strong>Synchronization:</strong> Locking primitives, RCU, and atomic operations</li>
                            <li><strong>Timers & Workqueues:</strong> Deferred work execution and time management</li>
                            <li><strong>Module System:</strong> Dynamic loading and unloading of kernel modules</li>
                            <li><strong>Power Management:</strong> CPU idle states and power-saving mechanisms</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Architecture Overview -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🏗️ Core Kernel Architecture</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="architecture-diagram">
                        <pre class="architecture-ascii">
┌─────────────────────────────────────────────────────────────┐
│                    User Space Applications                   │
├─────────────────────────────────────────────────────────────┤
│                     System Call Interface                   │
│                  (syscall entry/exit points)                │
├─────────────────────────────────────────────────────────────┤
│                      Core Kernel Services                   │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │   Process   │  Scheduler  │   System    │  Interrupt  │  │
│  │ Management  │    (CFS)    │    Calls    │  Handling   │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │   Locking   │     RCU     │   Timers    │ Workqueues  │  │
│  │ Primitives  │             │             │             │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │   Module    │    Power    │   Audit     │   Tracing   │  │
│  │   System    │ Management  │   System    │   System    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    Hardware Abstraction                     │
└─────────────────────────────────────────────────────────────┘
                        </pre>
                    </div>
                    
                    <h3>🔧 Core Components</h3>
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Process Management</h4>
                                <p class="card-subtitle">Process lifecycle and task structures</p>
                            </div>
                            <p>
                                Manages process creation (fork/clone), termination, and the task_struct 
                                data structure that represents every process and thread in the system.
                            </p>
                            <p><strong>Key Files:</strong> <code>fork.c</code>, <code>exit.c</code>, <code>exec_domain.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Scheduler (CFS)</h4>
                                <p class="card-subtitle">CPU time allocation and load balancing</p>
                            </div>
                            <p>
                                The Completely Fair Scheduler ensures fair CPU time distribution among 
                                processes using red-black trees and virtual runtime accounting.
                            </p>
                            <p><strong>Key Files:</strong> <code>sched/core.c</code>, <code>sched/fair.c</code>, <code>sched/rt.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">System Calls</h4>
                                <p class="card-subtitle">User-kernel interface</p>
                            </div>
                            <p>
                                Provides the interface for user space applications to request kernel 
                                services through a well-defined API with proper privilege checking.
                            </p>
                            <p><strong>Key Files:</strong> <code>sys.c</code>, <code>sys_ni.c</code>, <code>entry/</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Interrupt Handling</h4>
                                <p class="card-subtitle">Hardware and software interrupts</p>
                            </div>
                            <p>
                                Manages interrupt request (IRQ) handling, including top-half and 
                                bottom-half processing, softirqs, and tasklets.
                            </p>
                            <p><strong>Key Files:</strong> <code>irq/</code>, <code>softirq.c</code>, <code>irq_work.c</code></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Process Management -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔄 Process Management</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>struct task_struct</h3>
                        <div class="api-signature">
                            <code>struct task_struct</code> - The fundamental process descriptor
                        </div>
                        <p>
                            The task_struct is the central data structure representing every process 
                            and thread in the Linux system. It contains all information needed to 
                            manage a process's execution state, memory, files, and scheduling.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct task_struct {
    volatile long __state;        /* Process state (TASK_RUNNING, etc.) */
    void *stack;                  /* Kernel stack pointer */
    atomic_t usage;               /* Reference count */
    unsigned int flags;           /* Process flags (PF_EXITING, etc.) */
    
    /* Scheduling information */
    int prio, static_prio, normal_prio;
    struct sched_entity se;       /* CFS scheduling entity */
    struct sched_rt_entity rt;    /* RT scheduling entity */
    struct sched_dl_entity dl;    /* Deadline scheduling entity */
    
    /* Process hierarchy */
    struct task_struct *parent;   /* Parent process */
    struct list_head children;    /* List of child processes */
    struct list_head sibling;     /* Sibling processes */
    
    /* Memory management */
    struct mm_struct *mm;         /* Memory descriptor */
    struct mm_struct *active_mm;  /* Active memory descriptor */
    
    /* File system information */
    struct fs_struct *fs;         /* File system information */
    struct files_struct *files;   /* Open file descriptors */
    
    /* Signal handling */
    struct signal_struct *signal; /* Signal handlers */
    struct sighand_struct *sighand; /* Signal handler data */
    
    /* Process credentials */
    const struct cred *real_cred; /* Real credentials */
    const struct cred *cred;      /* Effective credentials */
    
    /* Process identification */
    pid_t pid;                    /* Process ID */
    pid_t tgid;                   /* Thread group ID */
    
    /* Timing information */
    u64 utime, stime;             /* User and system time */
    u64 start_time;               /* Process start time */
    
    /* Communication */
    struct task_struct *group_leader; /* Thread group leader */
    
    /* Namespaces */
    struct nsproxy *nsproxy;      /* Namespace proxy */
    
    /* Control groups */
    struct css_set *cgroups;      /* Control group memberships */
};</code></pre>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>Process Creation APIs</h3>
                        
                        <div class="api-function">
                            <div class="api-signature">
                                <code>long kernel_clone(struct kernel_clone_args *args)</code>
                            </div>
                            <p><strong>Purpose:</strong> Create a new process or thread</p>
                            <p><strong>Parameters:</strong></p>
                            <ul>
                                <li><code class="parameter-type">struct kernel_clone_args *</code> <strong>args</strong> - Clone arguments structure</li>
                            </ul>
                            <p><strong>Returns:</strong> <code class="return-type">long</code> - PID of new process or error code</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* Create a new process */
struct kernel_clone_args args = {
    .flags = CLONE_VM | CLONE_FS | CLONE_FILES | CLONE_SIGHAND,
    .exit_signal = SIGCHLD,
    .stack = 0,
    .stack_size = 0,
};

pid_t new_pid = kernel_clone(&args);
if (new_pid < 0) {
    pr_err("Failed to create process: %ld\n", new_pid);
    return new_pid;
}

pr_info("Created new process with PID: %d\n", new_pid);</code></pre>
                            </div>
                        </div>

                        <div class="api-function">
                            <div class="api-signature">
                                <code>void do_exit(long code)</code>
                            </div>
                            <p><strong>Purpose:</strong> Terminate the current process</p>
                            <p><strong>Parameters:</strong></p>
                            <ul>
                                <li><code class="parameter-type">long</code> <strong>code</strong> - Exit code</li>
                            </ul>
                            <p><strong>Returns:</strong> <code class="return-type">void</code> - Does not return</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* Process termination with cleanup */
static void cleanup_and_exit(int exit_code)
{
    /* Perform cleanup operations */
    cleanup_resources();
    
    /* Set exit code and terminate */
    do_exit(exit_code);
    /* This function never returns */
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Scheduler -->
            <section class="content-section">
                <div class="section-header">
                    <h2>⏰ Completely Fair Scheduler (CFS)</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <p>
                        The Completely Fair Scheduler is Linux's default process scheduler that aims 
                        to provide fair CPU time to all processes. It uses a red-black tree to 
                        organize runnable tasks and virtual runtime to ensure fairness.
                    </p>

                    <div class="api-section">
                        <h3>Scheduling Classes</h3>
                        <div class="config-grid">
                            <div class="config-item">
                                <h4>CFS (SCHED_NORMAL)</h4>
                                <p>Default scheduler for normal processes using virtual runtime and red-black trees</p>
                            </div>
                            <div class="config-item">
                                <h4>RT (SCHED_FIFO/RR)</h4>
                                <p>Real-time scheduler with fixed priorities and preemption</p>
                            </div>
                            <div class="config-item">
                                <h4>Deadline (SCHED_DEADLINE)</h4>
                                <p>Deadline scheduler for time-critical tasks with guaranteed execution</p>
                            </div>
                            <div class="config-item">
                                <h4>Idle (SCHED_IDLE)</h4>
                                <p>Low-priority scheduler for background tasks</p>
                            </div>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>Key Scheduling APIs</h3>
                        
                        <div class="api-function">
                            <div class="api-signature">
                                <code>void schedule(void)</code>
                            </div>
                            <p><strong>Purpose:</strong> Voluntarily give up the CPU and reschedule</p>
                            <p><strong>Returns:</strong> <code class="return-type">void</code></p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* Voluntary scheduling point */
static int worker_thread(void *data)
{
    while (!kthread_should_stop()) {
        /* Do some work */
        process_work_item();
        
        /* Check if we should yield CPU */
        if (need_resched()) {
            schedule();
        }
        
        /* Check for signals */
        if (signal_pending(current)) {
            break;
        }
    }
    
    return 0;
}</code></pre>
                            </div>
                        </div>

                        <div class="api-function">
                            <div class="api-signature">
                                <code>void wake_up_process(struct task_struct *p)</code>
                            </div>
                            <p><strong>Purpose:</strong> Wake up a sleeping process</p>
                            <p><strong>Parameters:</strong></p>
                            <ul>
                                <li><code class="parameter-type">struct task_struct *</code> <strong>p</strong> - Process to wake up</li>
                            </ul>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>/* Wake up a waiting process */
static void complete_work(struct work_item *work)
{
    /* Mark work as completed */
    work->completed = true;
    
    /* Wake up any waiting processes */
    if (work->waiting_task) {
        wake_up_process(work->waiting_task);
        work->waiting_task = NULL;
    }
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- System Calls -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔌 System Call Interface</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <p>
                        System calls provide the interface between user space applications and the kernel.
                        They allow controlled access to kernel services while maintaining security and stability.
                    </p>

                    <div class="api-section">
                        <h3>System Call Implementation</h3>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* System call definition macro */
SYSCALL_DEFINE3(read, unsigned int, fd, char __user *, buf, size_t, count)
{
    struct fd f = fdget_pos(fd);
    ssize_t ret = -EBADF;

    if (f.file) {
        loff_t pos = file_pos_read(f.file);
        ret = vfs_read(f.file, buf, count, &pos);
        if (ret >= 0)
            file_pos_write(f.file, pos);
        fdput_pos(f);
    }

    return ret;
}

/* System call table entry */
#define __NR_read 0
asmlinkage long sys_read(unsigned int fd, char __user *buf, size_t count);</code></pre>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>Common System Call Categories</h3>
                        <div class="config-grid">
                            <div class="config-item">
                                <h4>Process Control</h4>
                                <p><code>fork()</code>, <code>exec()</code>, <code>exit()</code>, <code>wait()</code></p>
                            </div>
                            <div class="config-item">
                                <h4>File Operations</h4>
                                <p><code>open()</code>, <code>read()</code>, <code>write()</code>, <code>close()</code></p>
                            </div>
                            <div class="config-item">
                                <h4>Memory Management</h4>
                                <p><code>mmap()</code>, <code>munmap()</code>, <code>brk()</code>, <code>mprotect()</code></p>
                            </div>
                            <div class="config-item">
                                <h4>Inter-Process Communication</h4>
                                <p><code>pipe()</code>, <code>socket()</code>, <code>msgget()</code>, <code>semget()</code></p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Synchronization -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔒 Synchronization Primitives</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>Locking Mechanisms</h3>

                        <div class="api-function">
                            <div class="api-signature">
                                <code>void spin_lock(spinlock_t *lock)</code>
                            </div>
                            <p><strong>Purpose:</strong> Acquire a spinlock (busy-wait)</p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>static DEFINE_SPINLOCK(my_lock);
static int shared_counter = 0;

void increment_counter(void)
{
    unsigned long flags;

    /* Disable interrupts and acquire lock */
    spin_lock_irqsave(&my_lock, flags);

    /* Critical section */
    shared_counter++;

    /* Release lock and restore interrupts */
    spin_unlock_irqrestore(&my_lock, flags);
}</code></pre>
                            </div>
                        </div>

                        <div class="api-function">
                            <div class="api-signature">
                                <code>void mutex_lock(struct mutex *lock)</code>
                            </div>
                            <p><strong>Purpose:</strong> Acquire a mutex (sleeping lock)</p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>static DEFINE_MUTEX(resource_mutex);

int access_shared_resource(void)
{
    int ret;

    /* Acquire mutex (may sleep) */
    ret = mutex_lock_interruptible(&resource_mutex);
    if (ret)
        return ret;

    /* Critical section - can sleep here */
    ret = perform_long_operation();

    /* Release mutex */
    mutex_unlock(&resource_mutex);

    return ret;
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>Read-Copy-Update (RCU)</h3>
                        <p>
                            RCU provides scalable read-mostly synchronization by allowing readers
                            to access data structures without locking while writers update them safely.
                        </p>

                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct my_data {
    int value;
    struct rcu_head rcu;
};

static struct my_data __rcu *global_ptr;

/* RCU reader */
int read_data(void)
{
    struct my_data *p;
    int value;

    rcu_read_lock();
    p = rcu_dereference(global_ptr);
    if (p)
        value = p->value;
    else
        value = -1;
    rcu_read_unlock();

    return value;
}

/* RCU writer */
void update_data(int new_value)
{
    struct my_data *new_p, *old_p;

    new_p = kmalloc(sizeof(*new_p), GFP_KERNEL);
    if (!new_p)
        return;

    new_p->value = new_value;

    old_p = rcu_dereference_protected(global_ptr, 1);
    rcu_assign_pointer(global_ptr, new_p);

    if (old_p)
        kfree_rcu(old_p, rcu);
}</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Timers and Workqueues -->
            <section class="content-section">
                <div class="section-header">
                    <h2>⏱️ Timers and Workqueues</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>Kernel Timers</h3>

                        <div class="api-function">
                            <div class="api-signature">
                                <code>void timer_setup(struct timer_list *timer, void (*callback)(struct timer_list *), unsigned int flags)</code>
                            </div>
                            <p><strong>Purpose:</strong> Initialize a kernel timer</p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>static struct timer_list my_timer;

static void timer_callback(struct timer_list *t)
{
    pr_info("Timer expired!\n");

    /* Reschedule timer for 5 seconds */
    mod_timer(&my_timer, jiffies + 5 * HZ);
}

static int init_timer_example(void)
{
    /* Initialize timer */
    timer_setup(&my_timer, timer_callback, 0);

    /* Start timer (expires in 5 seconds) */
    mod_timer(&my_timer, jiffies + 5 * HZ);

    return 0;
}

static void cleanup_timer_example(void)
{
    /* Delete timer */
    del_timer_sync(&my_timer);
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>Workqueues</h3>

                        <div class="api-function">
                            <div class="api-signature">
                                <code>bool schedule_work(struct work_struct *work)</code>
                            </div>
                            <p><strong>Purpose:</strong> Schedule work to be executed in process context</p>

                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">C</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code>static struct work_struct my_work;

static void work_handler(struct work_struct *work)
{
    pr_info("Work executed in process context\n");

    /* Can sleep here, access user memory, etc. */
    msleep(100);

    /* Perform work that requires process context */
    perform_blocking_operation();
}

static void schedule_deferred_work(void)
{
    /* Initialize work */
    INIT_WORK(&my_work, work_handler);

    /* Schedule work */
    schedule_work(&my_work);
}

/* Delayed work example */
static struct delayed_work delayed_work;

static void delayed_work_handler(struct work_struct *work)
{
    pr_info("Delayed work executed\n");
}

static void schedule_delayed_work_example(void)
{
    INIT_DELAYED_WORK(&delayed_work, delayed_work_handler);

    /* Schedule work to run in 10 seconds */
    schedule_delayed_work(&delayed_work, 10 * HZ);
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Configuration Options -->
            <section class="content-section">
                <div class="section-header">
                    <h2>⚙️ Configuration Options</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="config-grid">
                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_SMP</code></h4>
                            <p>Enable symmetric multi-processing support</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_PREEMPT</code></h4>
                            <p>Enable preemptible kernel for better responsiveness</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_RCU_EXPERT</code></h4>
                            <p>Enable expert-level RCU configuration options</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_CGROUPS</code></h4>
                            <p>Enable control groups for resource management</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_NAMESPACES</code></h4>
                            <p>Enable namespace support for containers</p>
                        </div>

                        <div class="config-item">
                            <h4><code class="config-option">CONFIG_AUDIT</code></h4>
                            <p>Enable system call auditing</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- File Organization -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📁 File Organization</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="file-tree">
                        <h3>kernel/ Directory Structure</h3>
                        <pre class="file-listing">
kernel/
├── <strong>fork.c</strong>              # Process creation (fork, clone)
├── <strong>exit.c</strong>              # Process termination
├── <strong>sys.c</strong>               # System call implementations
├── <strong>signal.c</strong>            # Signal handling
├── <strong>ptrace.c</strong>            # Process tracing
├── <strong>capability.c</strong>        # Capability management
├── <strong>cred.c</strong>              # Credential management
├── <strong>user.c</strong>              # User structure management
├── <strong>groups.c</strong>            # Group management
├── <strong>pid.c</strong>               # Process ID management
├── <strong>kthread.c</strong>           # Kernel thread management
├── <strong>workqueue.c</strong>         # Work queue implementation
├── <strong>softirq.c</strong>           # Soft interrupt handling
├── <strong>irq_work.c</strong>          # IRQ work implementation
├── <strong>panic.c</strong>             # Kernel panic handling
├── <strong>reboot.c</strong>            # System reboot
├── <strong>resource.c</strong>          # Resource management
├── <strong>params.c</strong>            # Kernel parameter handling
├── <strong>sysctl.c</strong>            # System control interface
├── <strong>module.c</strong>            # Module loading/unloading
├── <strong>kallsyms.c</strong>          # Kernel symbol table
├── <strong>printk/</strong>             # Kernel logging subsystem
│   ├── printk.c
│   ├── printk_safe.c
│   └── printk_ringbuffer.c
├── <strong>sched/</strong>              # Scheduler implementation
│   ├── core.c              # Core scheduler
│   ├── fair.c              # CFS implementation
│   ├── rt.c                # Real-time scheduler
│   ├── deadline.c          # Deadline scheduler
│   ├── idle.c              # Idle scheduler
│   ├── stop_task.c         # Stop task scheduler
│   ├── wait.c              # Wait queue implementation
│   ├── completion.c        # Completion mechanism
│   └── topology.c          # CPU topology
├── <strong>time/</strong>               # Time management
│   ├── timer.c             # Kernel timers
│   ├── hrtimer.c           # High-resolution timers
│   ├── tick-common.c       # Tick management
│   ├── tick-sched.c        # Tickless operation
│   ├── clocksource.c       # Clock source management
│   ├── clockevents.c       # Clock event management
│   ├── timekeeping.c       # System time keeping
│   └── posix-timers.c      # POSIX timer implementation
├── <strong>irq/</strong>                # Interrupt handling
│   ├── irqdesc.c           # IRQ descriptors
│   ├── handle.c            # IRQ handling
│   ├── manage.c            # IRQ management
│   ├── spurious.c          # Spurious IRQ detection
│   ├── resend.c            # IRQ resending
│   ├── chip.c              # IRQ chip management
│   └── proc.c              # IRQ /proc interface
├── <strong>locking/</strong>            # Locking primitives
│   ├── mutex.c             # Mutex implementation
│   ├── rwsem.c             # Read-write semaphores
│   ├── spinlock.c          # Spinlock implementation
│   ├── rtmutex.c           # RT mutex implementation
│   ├── lockdep.c           # Lock dependency checker
│   └── locktorture.c       # Lock torture testing
├── <strong>rcu/</strong>                # Read-Copy-Update
│   ├── tree.c              # Tree RCU implementation
│   ├── srcutree.c          # Sleepable RCU
│   ├── tasks.h             # RCU tasks
│   ├── update.c            # RCU update handling
│   └── rcutorture.c        # RCU torture testing
├── <strong>power/</strong>              # Power management
│   ├── main.c              # PM core
│   ├── suspend.c           # System suspend
│   ├── hibernate.c         # Hibernation
│   ├── snapshot.c          # Memory snapshots
│   ├── swap.c              # Swap for hibernation
│   ├── user.c              # Userspace PM interface
│   └── wakelock.c          # Wake locks
├── <strong>events/</strong>             # Performance events
│   ├── core.c              # Event core
│   ├── ring_buffer.c       # Event ring buffer
│   ├── callchain.c         # Call chain handling
│   └── hw_breakpoint.c     # Hardware breakpoints
├── <strong>trace/</strong>              # Tracing subsystem
│   ├── trace.c             # Core tracing
│   ├── ftrace.c            # Function tracer
│   ├── ring_buffer.c       # Trace ring buffer
│   ├── trace_events.c      # Trace events
│   ├── trace_printk.c      # Trace printk
│   └── blktrace.c          # Block layer tracing
├── <strong>cgroup/</strong>             # Control groups
│   ├── cgroup.c            # Core cgroup
│   ├── cgroup-v1.c         # Cgroup v1 support
│   ├── cpuset.c            # CPU set controller
│   ├── freezer.c           # Freezer controller
│   └── pids.c              # PID controller
├── <strong>bpf/</strong>                # Berkeley Packet Filter
│   ├── core.c              # BPF core
│   ├── syscall.c           # BPF system calls
│   ├── verifier.c          # BPF verifier
│   ├── hashtab.c           # BPF hash tables
│   └── arraymap.c          # BPF array maps
└── <strong>dma/</strong>                # DMA management
    ├── mapping.c           # DMA mapping
    ├── pool.c              # DMA pools
    ├── coherent.c          # Coherent DMA
    └── swiotlb.c           # Software IOMMU
                        </pre>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/search.js"></script>
    <script src="../../assets/js/syntax.js"></script>
</body>
</html>
