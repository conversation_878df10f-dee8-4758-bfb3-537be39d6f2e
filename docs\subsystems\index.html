<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Overview of Linux kernel core subsystems">
    <meta name="keywords" content="Linux kernel, subsystems, memory management, file systems, networking, device drivers">
    
    <title>Core Subsystems - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐧</text></svg>">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle expanded" aria-expanded="true">▼</button>
                    <a href="index.html" class="nav-link active">🔧 Core Subsystems</a>
                    <ul class="nav-submenu expanded">
                        <li class="nav-item">
                            <a href="mm/index.html" class="nav-link">💾 Memory Management (mm/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="#kernel" class="nav-link">⚙️ Core Kernel (kernel/) - Coming Soon</a>
                        </li>
                        <li class="nav-item">
                            <a href="#fs" class="nav-link">📁 File Systems (fs/) - Coming Soon</a>
                        </li>
                        <li class="nav-item">
                            <a href="#net" class="nav-link">🌐 Networking (net/) - Coming Soon</a>
                        </li>
                        <li class="nav-item">
                            <a href="#drivers" class="nav-link">🔌 Device Drivers (drivers/) - Coming Soon</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="../patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
                
                <li class="nav-item">
                    <a href="#api" class="nav-link">📚 API Reference - Coming Soon</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="../index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <span>Core Subsystems</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>🔧 Linux Kernel Core Subsystems</h1>
                </div>
                <div class="section-content">
                    <p>
                        The Linux kernel is organized into several major subsystems, each responsible 
                        for different aspects of system operation. This section provides comprehensive 
                        documentation for each subsystem.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">📋 Available Documentation</h3>
                        </div>
                        <p>
                            Currently available subsystem documentation. Additional subsystems 
                            are being documented and will be added soon.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Available Subsystems -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📚 Available Subsystems</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">💾 Memory Management (mm/)</h3>
                                <p class="card-subtitle">✅ Complete Documentation Available</p>
                            </div>
                            <p>
                                Comprehensive documentation for the memory management subsystem including 
                                virtual memory, page allocation, slab allocators, and memory reclaim.
                            </p>
                            <p><strong>Covers:</strong></p>
                            <ul>
                                <li>Buddy allocator and page management</li>
                                <li>SLUB/SLAB allocators</li>
                                <li>Virtual memory areas (VMAs)</li>
                                <li>Memory reclaim and swap</li>
                                <li>NUMA memory management</li>
                                <li>API reference and coding patterns</li>
                            </ul>
                            <a href="mm/index.html" class="nav-link">📖 Read Documentation →</a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Coming Soon -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🚧 Coming Soon</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">⚙️ Core Kernel (kernel/)</h3>
                                <p class="card-subtitle">🚧 Documentation In Progress</p>
                            </div>
                            <p>Process management, scheduling, system calls, and core kernel functionality.</p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">📁 File Systems (fs/)</h3>
                                <p class="card-subtitle">🚧 Documentation In Progress</p>
                            </div>
                            <p>Virtual File System (VFS) layer and file system implementations.</p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🌐 Networking (net/)</h3>
                                <p class="card-subtitle">🚧 Documentation In Progress</p>
                            </div>
                            <p>TCP/IP stack, socket layer, and network protocols.</p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🔌 Device Drivers (drivers/)</h3>
                                <p class="card-subtitle">🚧 Documentation In Progress</p>
                            </div>
                            <p>Device driver frameworks and hardware abstraction.</p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🏗️ Architecture Support (arch/)</h3>
                                <p class="card-subtitle">🚧 Documentation In Progress</p>
                            </div>
                            <p>Platform-specific implementations for different CPU architectures.</p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🔐 Security (security/)</h3>
                                <p class="card-subtitle">🚧 Documentation In Progress</p>
                            </div>
                            <p>Security frameworks, LSM, and access control mechanisms.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- How to Navigate -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🧭 How to Navigate</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="navigation-tips">
                        <div class="tip-item">
                            <h4>📖 Reading Documentation</h4>
                            <p>Each subsystem page includes:</p>
                            <ul>
                                <li>Architecture overview and key components</li>
                                <li>Important data structures and APIs</li>
                                <li>Common coding patterns and examples</li>
                                <li>Configuration options and performance tips</li>
                                <li>Cross-references to related subsystems</li>
                            </ul>
                        </div>
                        
                        <div class="tip-item">
                            <h4>🔍 Using Search</h4>
                            <p>Use the search box in the header to find:</p>
                            <ul>
                                <li>Specific functions or data structures</li>
                                <li>Configuration options</li>
                                <li>Code examples and patterns</li>
                                <li>Cross-subsystem information</li>
                            </ul>
                        </div>
                        
                        <div class="tip-item">
                            <h4>🎯 Learning Path</h4>
                            <p>Recommended reading order:</p>
                            <ol>
                                <li>Start with <a href="../index.html">kernel overview</a></li>
                                <li>Read <a href="../patterns/index.html">coding patterns</a></li>
                                <li>Explore specific subsystems of interest</li>
                                <li>Reference API documentation as needed</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/search.js"></script>
    <script src="../assets/js/syntax.js"></script>
</body>
</html>
