<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive documentation for Linux kernel device drivers subsystem (drivers/)">
    <meta name="keywords" content="Linux kernel, device drivers, device model, PCI, USB, block devices, character devices">
    
    <title>Device Drivers (drivers/) - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐧</text></svg>">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="../../index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="../../index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle expanded" aria-expanded="true">▼</button>
                    <a href="../index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu expanded">
                        <li class="nav-item">
                            <a href="../mm/index.html" class="nav-link">💾 Memory Management (mm/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../kernel/index.html" class="nav-link">⚙️ Core Kernel (kernel/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../fs/index.html" class="nav-link">📁 File Systems (fs/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="../net/index.html" class="nav-link">🌐 Networking (net/)</a>
                        </li>
                        <li class="nav-item">
                            <a href="index.html" class="nav-link active">🔌 Device Drivers (drivers/)</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="../../patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
                
                <li class="nav-item">
                    <a href="../../api/index.html" class="nav-link">📚 API Reference</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="../../index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="../index.html">Core Subsystems</a>
                <span class="breadcrumb-separator">›</span>
                <span>Device Drivers (drivers/)</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>🔌 Device Drivers Subsystem (drivers/)</h1>
                </div>
                <div class="section-content">
                    <p>
                        The device drivers subsystem provides the interface between the kernel 
                        and hardware devices. It implements the Linux device model, manages 
                        device discovery and initialization, and provides frameworks for 
                        different types of devices including block, character, and network devices.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🎯 Core Responsibilities</h3>
                        </div>
                        <ul>
                            <li><strong>Device Model:</strong> Unified device representation and management</li>
                            <li><strong>Bus Drivers:</strong> PCI, USB, I2C, SPI, and other bus implementations</li>
                            <li><strong>Block Devices:</strong> Storage device drivers and block I/O management</li>
                            <li><strong>Character Devices:</strong> Serial devices, terminals, and misc devices</li>
                            <li><strong>Network Drivers:</strong> Ethernet, wireless, and other network interfaces</li>
                            <li><strong>Graphics Drivers:</strong> GPU drivers and display management</li>
                            <li><strong>Input Devices:</strong> Keyboard, mouse, touchscreen, and HID devices</li>
                            <li><strong>Power Management:</strong> Device power states and runtime PM</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Architecture Overview -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🏗️ Device Driver Architecture</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="architecture-diagram">
                        <pre class="architecture-ascii">
┌─────────────────────────────────────────────────────────────┐
│                    User Space Applications                   │
├─────────────────────────────────────────────────────────────┤
│                     System Call Interface                   │
│                  (open, read, write, ioctl)                 │
├─────────────────────────────────────────────────────────────┤
│                      Device Files                           │
│                   (/dev/sda, /dev/tty, etc.)               │
├─────────────────────────────────────────────────────────────┤
│                    Linux Device Model                       │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │   Device    │    Driver   │     Bus     │   Class     │  │
│  │   Objects   │   Objects   │   Objects   │  Objects    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    Device Drivers                           │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │    Block    │  Character  │   Network   │   Graphics  │  │
│  │   Drivers   │   Drivers   │   Drivers   │   Drivers   │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │    Input    │    Sound    │    GPIO     │   Thermal   │  │
│  │   Drivers   │   Drivers   │   Drivers   │   Drivers   │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                      Bus Layers                             │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │     PCI     │     USB     │     I2C     │     SPI     │  │
│  │             │             │             │             │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                     Hardware Devices                        │
│                 (CPU, Memory, Peripherals)                  │
└─────────────────────────────────────────────────────────────┘
                        </pre>
                    </div>
                    
                    <h3>🔧 Core Components</h3>
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Device Model</h4>
                                <p class="card-subtitle">Unified device representation</p>
                            </div>
                            <p>
                                Provides a unified way to represent devices, drivers, and buses 
                                in the kernel with automatic device discovery, driver binding, 
                                and power management.
                            </p>
                            <p><strong>Key Files:</strong> <code>base/core.c</code>, <code>base/driver.c</code>, <code>base/bus.c</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Block Devices</h4>
                                <p class="card-subtitle">Storage device management</p>
                            </div>
                            <p>
                                Manages storage devices like hard drives, SSDs, and optical drives 
                                with support for partitioning, I/O scheduling, and device mapping.
                            </p>
                            <p><strong>Key Directory:</strong> <code>block/</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Character Devices</h4>
                                <p class="card-subtitle">Stream-oriented devices</p>
                            </div>
                            <p>
                                Handles devices that provide character-based I/O like terminals, 
                                serial ports, and miscellaneous devices with direct read/write access.
                            </p>
                            <p><strong>Key Directory:</strong> <code>char/</code></p>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Bus Drivers</h4>
                                <p class="card-subtitle">Hardware bus implementations</p>
                            </div>
                            <p>
                                Implements support for various hardware buses including PCI, USB, 
                                I2C, SPI, and others that connect devices to the system.
                            </p>
                            <p><strong>Key Directories:</strong> <code>pci/</code>, <code>usb/</code>, <code>i2c/</code></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Device Model -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🔗 Linux Device Model</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>struct device</h3>
                        <div class="api-signature">
                            <code>struct device</code> - Represents a physical or virtual device
                        </div>
                        <p>
                            The device structure is the fundamental representation of any device 
                            in the Linux kernel, containing device metadata, driver binding 
                            information, and power management state.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct device {
    struct device *parent;        /* Parent device */
    struct device_private *p;     /* Private data */
    
    struct kobject kobj;          /* Kernel object */
    const char *init_name;        /* Initial device name */
    const struct device_type *type; /* Device type */
    
    struct bus_type *bus;         /* Bus type */
    struct device_driver *driver; /* Associated driver */
    void *platform_data;         /* Platform-specific data */
    void *driver_data;           /* Driver-specific data */
    
    struct dev_links_info links;  /* Device links */
    struct dev_pm_info power;     /* Power management info */
    struct dev_pm_domain *pm_domain; /* PM domain */
    
    struct irq_domain *msi_domain; /* MSI interrupt domain */
    
    struct list_head devres_head; /* Device resources */
    
    struct klist_node knode_class; /* Class list node */
    struct class *class;          /* Device class */
    const struct attribute_group **groups; /* Attribute groups */
    
    void (*release)(struct device *dev); /* Release function */
    struct iommu_group *iommu_group; /* IOMMU group */
    struct iommu_fwspec *iommu_fwspec; /* IOMMU firmware spec */
    
    bool offline_disabled:1;      /* Offline disabled */
    bool offline:1;               /* Device offline */
    bool of_node_reused:1;        /* OF node reused */
    bool state_synced:1;          /* State synchronized */
};</code></pre>
                        </div>
                    </div>

                    <div class="api-section">
                        <h3>struct device_driver</h3>
                        <div class="api-signature">
                            <code>struct device_driver</code> - Represents a device driver
                        </div>
                        <p>
                            The device_driver structure represents a driver that can handle 
                            one or more devices, containing probe/remove functions and 
                            driver-specific operations.
                        </p>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>struct device_driver {
    const char *name;             /* Driver name */
    struct bus_type *bus;         /* Bus type */
    
    struct module *owner;         /* Module owner */
    const char *mod_name;         /* Module name */
    
    bool suppress_bind_attrs;     /* Suppress bind attributes */
    enum probe_type probe_type;   /* Probe type */
    
    const struct of_device_id *of_match_table; /* OF match table */
    const struct acpi_device_id *acpi_match_table; /* ACPI match */
    
    int (*probe)(struct device *dev);    /* Probe function */
    int (*remove)(struct device *dev);   /* Remove function */
    void (*shutdown)(struct device *dev); /* Shutdown function */
    int (*suspend)(struct device *dev, pm_message_t state); /* Suspend */
    int (*resume)(struct device *dev);   /* Resume function */
    
    const struct attribute_group **groups; /* Attribute groups */
    const struct attribute_group **dev_groups; /* Device groups */
    
    const struct dev_pm_ops *pm; /* Power management ops */
    void (*coredump)(struct device *dev); /* Core dump function */
    
    struct driver_private *p;     /* Private data */
};</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Driver Development -->
            <section class="content-section">
                <div class="section-header">
                    <h2>⚙️ Driver Development</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="api-section">
                        <h3>Basic Driver Template</h3>
                        
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-language">C</span>
                                <button class="code-copy">Copy</button>
                            </div>
                            <pre><code>/* Example platform driver */
#include <linux/module.h>
#include <linux/platform_device.h>
#include <linux/of.h>

struct my_device_data {
    void __iomem *base;
    int irq;
    struct device *dev;
};

static int my_driver_probe(struct platform_device *pdev)
{
    struct my_device_data *data;
    struct resource *res;
    int ret;
    
    dev_info(&pdev->dev, "Probing device\n");
    
    /* Allocate device data */
    data = devm_kzalloc(&pdev->dev, sizeof(*data), GFP_KERNEL);
    if (!data)
        return -ENOMEM;
    
    data->dev = &pdev->dev;
    
    /* Get memory resource */
    res = platform_get_resource(pdev, IORESOURCE_MEM, 0);
    data->base = devm_ioremap_resource(&pdev->dev, res);
    if (IS_ERR(data->base))
        return PTR_ERR(data->base);
    
    /* Get IRQ */
    data->irq = platform_get_irq(pdev, 0);
    if (data->irq < 0)
        return data->irq;
    
    /* Request IRQ */
    ret = devm_request_irq(&pdev->dev, data->irq, my_irq_handler,
                          0, dev_name(&pdev->dev), data);
    if (ret) {
        dev_err(&pdev->dev, "Failed to request IRQ: %d\n", ret);
        return ret;
    }
    
    /* Store driver data */
    platform_set_drvdata(pdev, data);
    
    dev_info(&pdev->dev, "Device probed successfully\n");
    return 0;
}

static int my_driver_remove(struct platform_device *pdev)
{
    struct my_device_data *data = platform_get_drvdata(pdev);
    
    dev_info(&pdev->dev, "Removing device\n");
    
    /* Cleanup is automatic with devm_* functions */
    
    return 0;
}

static const struct of_device_id my_driver_of_match[] = {
    { .compatible = "vendor,my-device" },
    { /* sentinel */ }
};
MODULE_DEVICE_TABLE(of, my_driver_of_match);

static struct platform_driver my_driver = {
    .probe = my_driver_probe,
    .remove = my_driver_remove,
    .driver = {
        .name = "my-driver",
        .of_match_table = my_driver_of_match,
    },
};

module_platform_driver(my_driver);

MODULE_AUTHOR("Your Name");
MODULE_DESCRIPTION("My Device Driver");
MODULE_LICENSE("GPL v2");</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Device Types -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📱 Device Types</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="subsystem-grid">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Block Devices</h3>
                                <p class="card-subtitle">Storage devices</p>
                            </div>
                            <p>
                                Devices that store data in fixed-size blocks, typically storage 
                                devices like hard drives, SSDs, and USB storage.
                            </p>
                            <p><strong>Examples:</strong></p>
                            <ul>
                                <li>SATA/PATA hard drives</li>
                                <li>NVMe SSDs</li>
                                <li>USB mass storage</li>
                                <li>SD/MMC cards</li>
                                <li>Optical drives (CD/DVD/Blu-ray)</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Character Devices</h3>
                                <p class="card-subtitle">Stream-oriented devices</p>
                            </div>
                            <p>
                                Devices that handle data as a stream of characters, providing 
                                sequential access without fixed block sizes.
                            </p>
                            <p><strong>Examples:</strong></p>
                            <ul>
                                <li>Serial ports (ttyS*, ttyUSB*)</li>
                                <li>Terminals (tty*, pts/*)</li>
                                <li>Random number generators (/dev/random)</li>
                                <li>GPIO devices</li>
                                <li>Watchdog timers</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Network Devices</h3>
                                <p class="card-subtitle">Network interfaces</p>
                            </div>
                            <p>
                                Devices that provide network connectivity, handling packet 
                                transmission and reception over various network protocols.
                            </p>
                            <p><strong>Examples:</strong></p>
                            <ul>
                                <li>Ethernet controllers</li>
                                <li>Wireless adapters (802.11)</li>
                                <li>Bluetooth adapters</li>
                                <li>Cellular modems</li>
                                <li>Virtual network interfaces</li>
                            </ul>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Input Devices</h3>
                                <p class="card-subtitle">Human interface devices</p>
                            </div>
                            <p>
                                Devices that capture user input and convert it into events 
                                that applications can process.
                            </p>
                            <p><strong>Examples:</strong></p>
                            <ul>
                                <li>Keyboards (PS/2, USB)</li>
                                <li>Mice and trackpads</li>
                                <li>Touchscreens</li>
                                <li>Game controllers</li>
                                <li>Joysticks and gamepads</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
    <script src="../../assets/js/search.js"></script>
    <script src="../../assets/js/syntax.js"></script>
</body>
</html>
