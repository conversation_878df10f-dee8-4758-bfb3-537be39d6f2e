<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Linux kernel configuration options and build system documentation">
    <meta name="keywords" content="Linux kernel, configuration, Kconfig, build system, kernel options">
    
    <title>Configuration - Linux Kernel Documentation</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/syntax.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐧</text></svg>">
</head>
<body>
    <div class="main-layout">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    🐧 Linux Kernel Docs
                </a>
                
                <div class="header-controls">
                    <!-- Search -->
                    <div class="search-container">
                        <input type="search" 
                               class="search-input" 
                               placeholder="Search documentation..."
                               aria-label="Search documentation"
                               autocomplete="off">
                        <div class="search-results" role="listbox" aria-label="Search results"></div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <button class="theme-button" aria-label="Select theme" aria-haspopup="true">
                            Light
                        </button>
                        <div class="theme-dropdown" role="menu">
                            <button class="theme-option" data-theme="light" role="menuitem">Light</button>
                            <button class="theme-option" data-theme="medium" role="menuitem">Medium</button>
                            <button class="theme-option" data-theme="dark" role="menuitem">Dark</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar" role="navigation" aria-label="Main navigation">
            <ul class="nav-tree">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">🏠 Home</a>
                </li>
                
                <li class="nav-item">
                    <button class="nav-toggle" aria-expanded="false">▶</button>
                    <a href="../subsystems/index.html" class="nav-link">🔧 Core Subsystems</a>
                    <ul class="nav-submenu">
                        <li class="nav-item">
                            <a href="../subsystems/mm/index.html" class="nav-link">💾 Memory Management</a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="../patterns/index.html" class="nav-link">🎯 Coding Patterns</a>
                </li>
                
                <li class="nav-item">
                    <a href="../api/index.html" class="nav-link">📚 API Reference</a>
                </li>
                
                <li class="nav-item">
                    <a href="index.html" class="nav-link active">⚙️ Configuration</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb" aria-label="Breadcrumb">
                <a href="../index.html">🏠 Home</a>
                <span class="breadcrumb-separator">›</span>
                <span>Configuration</span>
            </nav>

            <!-- Page Header -->
            <section class="content-section">
                <div class="section-header">
                    <h1>⚙️ Linux Kernel Configuration</h1>
                </div>
                <div class="section-content">
                    <p>
                        The Linux kernel is highly configurable, with thousands of configuration 
                        options that control which features are included in the kernel build. 
                        This section documents the configuration system and important options.
                    </p>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🎯 Configuration System Overview</h3>
                        </div>
                        <ul>
                            <li><strong>Kconfig:</strong> The kernel configuration language and system</li>
                            <li><strong>Build System:</strong> Makefiles and build process</li>
                            <li><strong>Configuration Tools:</strong> menuconfig, xconfig, and others</li>
                            <li><strong>Default Configs:</strong> Architecture-specific default configurations</li>
                            <li><strong>Module System:</strong> Loadable kernel modules vs built-in features</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Configuration Tools -->
            <section class="content-section">
                <div class="section-header">
                    <h2>🛠️ Configuration Tools</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="config-tools">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">📋 make menuconfig</h3>
                                <p class="card-subtitle">Text-based configuration interface</p>
                            </div>
                            <p>The most commonly used configuration tool with a text-based menu interface.</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">Bash</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code># Configure the kernel
make menuconfig

# Save configuration to specific file
make KCONFIG_CONFIG=my_config menuconfig

# Use existing config as base
cp /boot/config-$(uname -r) .config
make oldconfig</code></pre>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🖥️ make xconfig</h3>
                                <p class="card-subtitle">Qt-based graphical interface</p>
                            </div>
                            <p>Graphical configuration tool with search and filtering capabilities.</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">Bash</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code># Launch graphical configurator
make xconfig

# Requires Qt development libraries
sudo apt-get install qt5-qmake qtbase5-dev-tools</code></pre>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">⚡ Quick Configuration</h3>
                                <p class="card-subtitle">Automated configuration options</p>
                            </div>
                            <p>Quick ways to generate configurations for common use cases.</p>
                            
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-language">Bash</span>
                                    <button class="code-copy">Copy</button>
                                </div>
                                <pre><code># Use distribution config
make localmodconfig    # Only modules currently loaded
make localyesconfig    # Built-in instead of modules

# Default configurations
make defconfig         # Architecture default
make allmodconfig      # All features as modules
make allyesconfig      # All features built-in</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Important Configuration Categories -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📋 Important Configuration Categories</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="config-categories">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🏗️ Architecture Support</h3>
                            </div>
                            <div class="config-grid">
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_X86_64</code></h4>
                                    <p>Enable 64-bit x86 architecture support</p>
                                </div>
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_ARM64</code></h4>
                                    <p>Enable ARM 64-bit architecture support</p>
                                </div>
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_SMP</code></h4>
                                    <p>Symmetric Multi-Processing support</p>
                                </div>
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_NUMA</code></h4>
                                    <p>Non-Uniform Memory Access support</p>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">💾 Memory Management</h3>
                            </div>
                            <div class="config-grid">
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_SLUB</code></h4>
                                    <p>SLUB allocator (recommended)</p>
                                </div>
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_TRANSPARENT_HUGEPAGE</code></h4>
                                    <p>Transparent huge page support</p>
                                </div>
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_MEMORY_HOTPLUG</code></h4>
                                    <p>Memory hot-add/remove support</p>
                                </div>
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_SWAP</code></h4>
                                    <p>Virtual memory swap support</p>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🔐 Security Features</h3>
                            </div>
                            <div class="config-grid">
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_SECURITY</code></h4>
                                    <p>Enable security framework</p>
                                </div>
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_SECURITY_SELINUX</code></h4>
                                    <p>SELinux security module</p>
                                </div>
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_SECURITY_APPARMOR</code></h4>
                                    <p>AppArmor security module</p>
                                </div>
                                <div class="config-item">
                                    <h4><code class="config-option">CONFIG_HARDENED_USERCOPY</code></h4>
                                    <p>Harden user copy operations</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Status -->
            <section class="content-section">
                <div class="section-header">
                    <h2>📊 Documentation Status</h2>
                    <button class="section-toggle" aria-label="Toggle section">▼</button>
                </div>
                <div class="section-content">
                    <div class="status-info">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">🚧 Work in Progress</h3>
                            </div>
                            <p>
                                Comprehensive configuration documentation is being developed. 
                                This will include detailed explanations of all major configuration 
                                options and their effects.
                            </p>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">📅 Coming Soon</h3>
                            </div>
                            <ul>
                                <li>Complete Kconfig option reference</li>
                                <li>Build system documentation</li>
                                <li>Performance tuning guides</li>
                                <li>Security configuration best practices</li>
                                <li>Architecture-specific options</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/search.js"></script>
    <script src="../assets/js/syntax.js"></script>
</body>
</html>
